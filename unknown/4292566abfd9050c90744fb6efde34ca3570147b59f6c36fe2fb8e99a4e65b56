package com.xyy.saas.inquiry.im.server.mq.producer;

import com.xyy.saas.eventbus.rocketmq.annotation.EventBusProducer;
import com.xyy.saas.eventbus.rocketmq.core.original.EventBusRocketMQTemplate;
import com.xyy.saas.inquiry.im.server.mq.message.TranscodingTaskStartEvent;
import org.springframework.stereotype.Component;

/**
 * @Author: xucao
 * @Date: 2024/12/11 15:11
 * @Description: 转码任务开始事件生产者
 */
@Component
@EventBusProducer(
    topic = TranscodingTaskStartEvent.TOPIC
)
public class TranscodingTaskStartProducer extends EventBusRocketMQTemplate {

}
