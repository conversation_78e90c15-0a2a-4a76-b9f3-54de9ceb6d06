<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.xyy.saas</groupId>
    <artifactId>saas-inquiry-product</artifactId>
    <version>${revision}</version>
    <relativePath>../pom.xml</relativePath>
  </parent>

  <artifactId>saas-inquiry-product-api</artifactId>

  <properties>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
  </properties>


  <dependencies>
    <dependency>
      <groupId>com.xyy.saas</groupId>
      <artifactId>saas-inquiry-pojo</artifactId>
      <version>${revision}</version>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-validation</artifactId>
      <optional>true</optional>
    </dependency>
    <!--        <dependency>-->
    <!--            <groupId>cn.iocoder.boot</groupId>-->
    <!--            <artifactId>yudao-module-system-api</artifactId>-->
    <!--            <version>${yudao.version}</version>-->
    <!--        </dependency>-->

    <dependency>
      <groupId>com.belerweb</groupId>
      <artifactId>pinyin4j</artifactId>
      <version>2.5.1</version>
    </dependency>
  </dependencies>

</project>