package com.xyy.saas.inquiry.product.consts;

/**
 * desc
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
public interface ProductConstant {

    interface ApprovalStatus {
        /** 无需审批 */
        int UN_NEED_TODO = -2;
        /** 未开始 */
        int NOT_START = -1;
        /** 待审批 */
        int WAIT = 0;
        /** 审批中 */
        int RUNNING = 1;
        /** 审批通过 */
        int APPROVE = 2;
        /** 审批不通过 */
        int REJECT = 3;
        /** 已取消 */
        int CANCEL = 4;
    }
}
