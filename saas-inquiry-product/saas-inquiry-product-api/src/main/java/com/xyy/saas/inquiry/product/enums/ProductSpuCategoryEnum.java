package com.xyy.saas.inquiry.product.enums;


/**
 * desc 商品大类 spuCategory
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
public enum ProductSpuCategoryEnum {
    GENERAL_DRUGS(1,"普通药品"),
    TRADITIONAL_CHINESE_MEDICINE(2,"中药"),
    MEDICAL_APPARATUS_INSTRUMENTS(3,"医疗器械"),

    NON_DRUG(4,"非药"),

    GIFT_TYPE(5,"赠品"),

    HEALTH_FOOD(11,"保健食品")
    ;

    public final int code;
    public final String desc;

    ProductSpuCategoryEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * 根据desc获取枚举值
     *
     * @param desc
     * @return
     */
    public static ProductSpuCategoryEnum getByDesc(String desc) {
        for (ProductSpuCategoryEnum type : values()) {
            if (type.desc.equals(desc)) {
                return type;
            }
        }
        return null;
    }
}
