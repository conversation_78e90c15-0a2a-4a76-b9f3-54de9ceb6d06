package com.xyy.saas.inquiry.product.enums;

import com.xyy.saas.inquiry.util.PrefUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 目录类型枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum CatalogTypeEnum {

    /**
     * 医保目录
     */
    MEDICAL(1, "医保", PrefUtil.MEDICAL_CATALOG_PREF),

    /**
     * 监管目录
     */
    REGULATORY(3, "监管", PrefUtil.INTERNET_REGULATION_PREF);

    /**
     * 类型编码
     */
    private final Integer code;

    /**
     * 类型名称
     */
    private final String name;

    /**
     * 目录编码前缀
     */
    private final String prefPrefix;

    /**
     * 根据编码获取枚举
     *
     * @param code 编码
     * @return 枚举值
     */
    public static CatalogTypeEnum fromCode(Integer code) {
        for (CatalogTypeEnum type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }

    /**
     * 根据目录类型参数获取目录类型枚举, 默认为监管目录
     *
     * @param catalogType
     * @return
     */
    public static CatalogTypeEnum getCatalogTypeDefaultRegulatory(Integer catalogType) {
        if (catalogType == null) {
            return CatalogTypeEnum.REGULATORY; // 默认监管类型，保持向后兼容
        }
        CatalogTypeEnum catalogTypeEnum = CatalogTypeEnum.fromCode(catalogType);
        if (catalogTypeEnum == null) {
            throw new IllegalArgumentException("未知的目录类型编码: " + catalogType);
        }
        return catalogTypeEnum;
    }

    /**
     * 判断是否为医保类型
     *
     * @return 是否为医保类型
     */
    public boolean isMedical() {
        return this == MEDICAL;
    }

    /**
     * 判断是否为监管类型
     *
     * @return 是否为监管类型
     */
    public boolean isRegulatory() {
        return this == REGULATORY;
    }

}
