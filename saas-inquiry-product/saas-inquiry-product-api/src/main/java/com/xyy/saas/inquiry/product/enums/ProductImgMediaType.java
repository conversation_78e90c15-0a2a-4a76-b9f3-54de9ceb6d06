package com.xyy.saas.inquiry.product.enums;


/**
 * desc
 *  图片类型 0:纠错图1:批件2:外包装3:说明书4:精修图5：原图
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
public enum ProductImgMediaType {
    CORRECT((byte) 0, "纠错图"),
    BATCH((byte) 1, "批件"),
    OUTER_PACKAGING((byte) 2, "外包装"),
    INSTRUCTIONS((byte) 3, "说明书"),
    REFINEMENT((byte) 4, "精修图"),
    ORIGINAL((byte) 5, "原图"),
    ;

    public final byte code;
    public final String desc;

    ProductImgMediaType(byte code, String desc) {
        this.code = code;
        this.desc = desc;
    }


}
