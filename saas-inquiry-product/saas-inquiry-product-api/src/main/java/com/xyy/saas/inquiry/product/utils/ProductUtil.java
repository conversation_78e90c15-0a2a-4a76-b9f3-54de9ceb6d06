package com.xyy.saas.inquiry.product.utils;

import cn.hutool.extra.pinyin.PinyinUtil;
import cn.iocoder.yudao.framework.common.util.json.JsonUtils;
import com.xyy.saas.inquiry.constant.BasicConstant.FieldCompareGroup;
import com.xyy.saas.inquiry.model.FieldChangeRecord;
import com.xyy.saas.inquiry.product.api.product.dto.ProductInfoDto;
import com.xyy.saas.inquiry.product.api.product.dto.ProductStdlibDto;
import com.xyy.saas.inquiry.util.FieldCompareUtil;
import lombok.experimental.UtilityClass;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.StringUtils;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * desc
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
@UtilityClass
public class ProductUtil {

    /**
     * 获取商品名（先取品牌名称，如果为空则取通用名称）
     * @param product
     * @return
     */
    public String getShowName(ProductInfoDto product) {
        return StringUtils.defaultIfBlank(product.getBrandName(), product.getCommonName());
    }

    /**
     * 助记码
     * @param commonName
     * @param brandName
     * @return
     */
    public String getMnemonicCode(String commonName, String brandName) {
        // 通用名首字母 ｜ 商品名首字母 （去重）
        Set<String> letterSet = new HashSet<>();
        if (StringUtils.isNotBlank(commonName)) {
            letterSet.add(PinyinUtil.getFirstLetter(commonName, ""));
        }
        if (StringUtils.isNotEmpty(brandName)) {
            letterSet.add(PinyinUtil.getFirstLetter(brandName, ""));
        }
        return String.join("|", letterSet);
    }

    /**
     * 计算要素字段hash值
     */
    public String calcKeyPointHash(String commonName, String brandName, String barcode, String spec, String approvalNumber, String manufacturer) {
        // 计算商品要素字段(通用名+品牌名+条形码+规格+批准文号+生产厂家)的hash值
        String keyPoint = String.join("",
            StringUtils.defaultString(commonName),
            StringUtils.defaultString(brandName),
            StringUtils.defaultString(barcode),
            StringUtils.defaultString(spec),
            StringUtils.defaultString(approvalNumber),
            StringUtils.defaultString(manufacturer)
        );
        return DigestUtils.md5Hex(keyPoint);  // NOSONAR
    }

    /**
     * 组装商品信息变更内容
     * @param newProduct
     * @param origin
     * @return
     */
    public String getQualityChangeDetailContent(ProductInfoDto newProduct, ProductInfoDto origin) {
        // 通过反射判断2个对象的字段，循环遍历newProduct的所有字段（属性为null的可以忽略），比较与origin对应字段是否相同，如果不相同则记录一条变更记录
        List<FieldChangeRecord> diffList = FieldCompareUtil.compare(FieldCompareGroup.DEFAULT, newProduct, origin, true);
        if (diffList.isEmpty()) {
            return null;
        }
        return JsonUtils.toJsonString(diffList);
    }

    /**
     * 判断商品价格信息是否变更
     * @param newProduct
     * @param origin
     * @return
     */
    public boolean isPriceChanged(ProductInfoDto newProduct, ProductInfoDto origin) {
        // 通过反射判断2个对象的字段，循环遍历newProduct的所有字段（属性为null的可以忽略），比较与origin对应字段是否相同，如果不相同则记录一条变更记录
        List<FieldChangeRecord> diffList = FieldCompareUtil.compare(FieldCompareGroup.PRICE, newProduct, origin, true);
        return !diffList.isEmpty();
    }
}
