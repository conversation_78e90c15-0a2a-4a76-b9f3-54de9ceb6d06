package com.xyy.saas.inquiry.product.api.bpm;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class BpmBusinessRelationDto {

    @Schema(description = "ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "26585")
    private Long id;

    @Schema(description = "租户编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "21079")
    @NotNull(message = "租户编号不能为空")
    private Long tenantId;

    @Schema(description = "租户编号（总部）", requiredMode = Schema.RequiredMode.REQUIRED, example = "21079")
    @NotNull(message = "租户编号（总部）不能为空")
    private Long headTenantId;

    @Schema(description = "业务类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "业务类型不能为空")
    private Integer businessType;

    @Schema(description = "业务单据编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "22519")
    @NotNull(message = "业务单据编号不能为空")
    private String businessPref;

    @Schema(description = "流程实例的编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "4711")
    // @NotEmpty(message = "流程实例的编号不能为空")
    private String processInstanceId;

    @Schema(description = "开始时间")
    private LocalDateTime startTime;

    @Schema(description = "结束时间")
    private LocalDateTime endTime;

    @Schema(description = "申请人", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "申请人不能为空")
    private String applicant;

    @Schema(description = "审批状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    // @NotNull(message = "审批状态不能为空")
    private Integer approvalStatus;

    @Schema(description = "备注", requiredMode = Schema.RequiredMode.REQUIRED, example = "你猜")
    // @NotEmpty(message = "备注不能为空")
    private String remark;

}