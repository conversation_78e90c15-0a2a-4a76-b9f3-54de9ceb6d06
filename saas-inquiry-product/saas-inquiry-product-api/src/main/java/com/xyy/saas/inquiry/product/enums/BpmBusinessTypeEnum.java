package com.xyy.saas.inquiry.product.enums;

import java.util.Objects;

/**
 * desc 审批业务类型：首营商品审批、商品回收站恢复审批、提报商品总部审批、质量变更申请审批、售价调整单审批、
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
public enum BpmBusinessTypeEnum {

    PRODUCT_FIRST_APPROVE(1, "pd_product_first_apr", "首营商品审批", ProductStatusEnum.FIRST_AUDITING, ProductStatusEnum.FIRST_AUDIT_REJECT),
    // PRODUCT_RECYCLE_APPROVE(2, "pd_product_recycle_apr", "商品回收站恢复审批"),
    PRODUCT_TO_HEADQUARTERS_APPROVE(3, "pd_product_to_headquarters_apr", "门店商品总部审批", ProductStatusEnum.HEAD_AUDITING, ProductStatusEnum.HEAD_AUDIT_REJECT),
    // PRODUCT_QUALITY_CHANGE_APPROVE(4, "pd_product_quality_change_apr", "质量变更申请审批"),
    // PRODUCT_PRICE_ADJUSTMENT_APPROVE(5, "pd_product_price_adjustment_apr", "售价调整单审批"),

    // PRODUCT_UNBUNDLED_APPROVE(6, "pd_product_unbundled_apr", "拆零商品审批", ProductStatusEnum.UNBUNDLED_AUDITING, ProductStatusEnum.UNBUNDLED_AUDIT_REJECT),


    ;

    public final int code;
    public final String processDefinitionKey;
    public final String desc;
    public final ProductStatusEnum auditingStatus;
    public final ProductStatusEnum auditRejectStatus;

    BpmBusinessTypeEnum(int code, String processDefinitionKey, String desc, ProductStatusEnum auditingStatus, ProductStatusEnum auditRejectStatus) {
        this.code = code;
        this.processDefinitionKey = processDefinitionKey;
        this.desc = desc;
        this.auditingStatus = auditingStatus;
        this.auditRejectStatus = auditRejectStatus;
    }

    public static BpmBusinessTypeEnum getByCode(int code) {
        for (BpmBusinessTypeEnum bpmBusinessTypeEnum : BpmBusinessTypeEnum.values()) {
            if (bpmBusinessTypeEnum.code == code) {
                return bpmBusinessTypeEnum;
            }
        }
        return null;
    }

    public static BpmBusinessTypeEnum getByProcessDefinitionKey(String processDefinitionKey) {
        for (BpmBusinessTypeEnum bpmBusinessTypeEnum : BpmBusinessTypeEnum.values()) {
            if (bpmBusinessTypeEnum.processDefinitionKey.equals(processDefinitionKey)) {
                return bpmBusinessTypeEnum;
            }
        }
        return null;
    }

    public static BpmBusinessTypeEnum getByAuditingStatus(Integer auditingStatus) {
        for (BpmBusinessTypeEnum bpmBusinessTypeEnum : BpmBusinessTypeEnum.values()) {
            if (bpmBusinessTypeEnum.auditingStatus != null && Objects.equals(bpmBusinessTypeEnum.auditingStatus.code, auditingStatus)) {
                return bpmBusinessTypeEnum;
            }
        }
        return null;
    }

}
