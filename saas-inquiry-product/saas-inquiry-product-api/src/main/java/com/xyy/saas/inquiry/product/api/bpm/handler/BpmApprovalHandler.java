package com.xyy.saas.inquiry.product.api.bpm.handler;

import com.xyy.saas.inquiry.product.api.bpm.BpmBusinessRelationDto;
import com.xyy.saas.inquiry.product.enums.BpmBusinessTypeEnum;

/**
 * 审批流处理器接口
 */
public interface BpmApprovalHandler {

    /**
     * 处理审批结果
     * @param businessDto 审批业务数据
     */
    void handleApproval(BpmBusinessRelationDto businessDto);

    /**
     * 获取支持的业务类型
     * @return 业务类型
     */
    BpmBusinessTypeEnum[] supportBusinessTypes();
} 