package com.xyy.saas.inquiry.product.api.catalog.dto;

import com.xyy.saas.inquiry.pojo.BaseDto;
import lombok.*;

/**
 * 监管目录明细 DO
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RegulatoryCatalogDetailDTO extends BaseDto {

    /**
     * 主键ID
     */
    private Long id;
    /**
     * 目录ID
     */
    private Long catalogId;
    /**
     * 项目编码（标准库ID）
     */
    private Long projectCode;

    /**
     * 医保目录编码
     */
    // private String medicalCatalogCode;
    /**
     * 通用名
     */
    private String commonName;
    /**
     * 品牌名称
     */
    private String brandName;
    /**
     * 规格/型号
     */
    private String spec;
    /**
     * 条形码
     */
    private String barcode;
    /**
     * 生产厂家
     */
    private String manufacturer;
    /**
     * 批准文号
     */
    private String approvalNumber;
    /**
     * 是否禁用，默认否
     */
    private Boolean disable;
    /**
     * 备注
     */
    private String remark;

}