package com.xyy.saas.inquiry.product.enums;

import java.util.Objects;
import java.util.Optional;

/**
 * desc 商品状态：
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
public enum ProductStatusEnum {

    // 初始暂存
    TEMP(0, "暂存"),

    // 使用中
    USING(1, "使用中"),

    // 新品提报（中台审核）
    MID_AUDITING(11, "标品审核中"),
    MID_AUDIT_REJECT(12, "标品审核驳回"),

    // 门店建品（总部审核）
    HEAD_AUDITING(21, "总部审核中"),
    HEAD_AUDIT_REJECT(22, "总部审核驳回"),

    // 首营审核
    FIRST_AUDITING(31, "首营审核中"),
    FIRST_AUDIT_REJECT(32, "首营审核驳回"),


    ;

    public final int code;
    public final String desc;

    ProductStatusEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static ProductStatusEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (ProductStatusEnum statusEnum : ProductStatusEnum.values()) {
            if (Objects.equals(statusEnum.code, code)) {
                return statusEnum;
            }
        }
        return null;
    }

    public static String getDescByCode(Integer code) {
        return Optional.ofNullable(ProductStatusEnum.getByCode(code)).map(i -> i.desc).orElse(null);
    }

    // 判断商品状态是否在标品审核
    public static boolean isInMidAuditing(Integer status) {
        return status != null && (
                status.equals(ProductStatusEnum.MID_AUDITING.code)
                || status.equals(ProductStatusEnum.MID_AUDIT_REJECT.code)
        );
    }
}
