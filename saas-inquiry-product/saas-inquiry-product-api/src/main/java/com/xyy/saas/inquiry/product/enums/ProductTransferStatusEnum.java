package com.xyy.saas.inquiry.product.enums;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * desc 流转状态：初始、成功、失败
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
public enum ProductTransferStatusEnum {

    // 初始
    INIT(0, "初始", 0),
    // 成功
    SUCCESS(1, "成功", 1),
    // 失败
    FAIL(9, "失败", 9),

    // 新品提报（中台审核）
    MID_AUDIT_REJECT(12, "标品审核驳回", 2),

    // 门店建品（总部审核）
    HEAD_AUDIT_REJECT(22, "总部审核驳回", 2),
    ;

    public final int code;
    public final String desc;
    public final int approveStatus;

    ProductTransferStatusEnum(int code, String desc, int approveStatus) {
        this.code = code;
        this.desc = desc;
        this.approveStatus = approveStatus;
    }



    /**
     * approveStatus 映射 statusList
     * @return
     */
    public static List<Integer> getCodeListByApproveStatus(Integer approveStatus) {
        if (approveStatus == null) {
            return null;
        }
        return Arrays.stream(ProductTransferStatusEnum.values()).filter(i -> Objects.equals(approveStatus, i.approveStatus))
            .map(i -> i.code).toList();
    }
    /**
     * status 映射 approveStatus
     * @return
     */
    public static Optional<ProductTransferStatusEnum> getByCode(Integer status) {
        if (status == null) {
            return Optional.empty();
        }
        return Arrays.stream(ProductTransferStatusEnum.values())
            .filter(i -> Objects.equals(status, i.code))
            .findFirst();
    }
}
