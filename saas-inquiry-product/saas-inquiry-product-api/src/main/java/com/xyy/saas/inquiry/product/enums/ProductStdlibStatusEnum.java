package com.xyy.saas.inquiry.product.enums;

import java.util.Objects;
import java.util.Optional;

/**
 * desc 商品状态：
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
public enum ProductStdlibStatusEnum {

    // 初始暂存
    TEMP(0, "暂存"),

    // 使用中
    USING(1, "使用中"),

    // 新品提报（中台审核）
    MID_AUDITING(11, "标品审核中"),
    MID_AUDIT_REJECT(12, "标品审核驳回"),

    ;

    public final int code;
    public final String desc;

    ProductStdlibStatusEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static ProductStdlibStatusEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (ProductStdlibStatusEnum statusEnum : ProductStdlibStatusEnum.values()) {
            if (Objects.equals(statusEnum.code, code)) {
                return statusEnum;
            }
        }
        return null;
    }

    public static String getDescByCode(Integer code) {
        return Optional.ofNullable(ProductStdlibStatusEnum.getByCode(code)).map(i -> i.desc).orElse(null);
    }

    /**
     * 根据商品状态，获得商品标准库状态
     */
    public static ProductStdlibStatusEnum fromProductStatus(Integer productStatus) {
        if (productStatus == null) {
            return null;
        }
        return switch (ProductStatusEnum.getByCode(productStatus)) {
            case ProductStatusEnum.TEMP -> ProductStdlibStatusEnum.TEMP;
            case ProductStatusEnum.MID_AUDITING -> ProductStdlibStatusEnum.MID_AUDITING;
            case ProductStatusEnum.MID_AUDIT_REJECT -> ProductStdlibStatusEnum.MID_AUDIT_REJECT;
            default -> ProductStdlibStatusEnum.USING;
        };
    }
}
