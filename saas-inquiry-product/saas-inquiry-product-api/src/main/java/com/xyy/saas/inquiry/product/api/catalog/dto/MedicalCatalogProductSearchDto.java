package com.xyy.saas.inquiry.product.api.catalog.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serial;
import java.io.Serializable;
import java.util.List;
import lombok.Data;


@Schema(description = "管理后台 - 医保目录商品信息 分页查询请求 VO")
@Data
public class MedicalCatalogProductSearchDto implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;


    @Schema(description = "医保目录类别")
    private List<String> projectTypeList;

    @Schema(description = "商品名称（模糊匹配）")
    private String mixedNameQuery;
    @Schema(description = "商品名称（自然搜索）")
    private String natureNameQuery;

    @Schema(description = "规格型号")
    private String spec;
    @Schema(description = "规格型号（模糊匹配）")
    private String specLike;

    @Schema(description = "生产厂家")
    private String manufacturer;
    @Schema(description = "生产厂家（模糊匹配）")
    private String manufacturerLike;
    @Schema(description = "批准文号")
    private String approvalNumber;
    @Schema(description = "批准文号集合")
    private List<String> approvalNumberList;
    @Schema(description = "自建标准库ID")
    private List<Long> idList;
    @Schema(description = "商品通用名集合")
    private List<String> commonNameList;

    @Schema(description = "停用状态")
    private Boolean disable;

    @Schema(description = "医保目录目录")
    private Long catalogId;

}
