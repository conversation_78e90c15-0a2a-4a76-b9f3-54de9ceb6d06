package com.xyy.saas.inquiry.product.api.catalog.dto;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

/**
 * 医保目录明细 DO
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MedicalCatalogDetailDTO extends BaseDO {

    /**
     * 主键ID
     */
    private Long id;
    
    /**
     * 目录ID
     */
    private Long catalogId;
    
    /**
     * 医疗目录编码
     */
    private String projectCode;
    
    /**
     * 医疗目录名称
     */
    private String projectName;
    
    /**
     * 品牌名称
     */
    private String brandName;
    
    /**
     * 助记码
     */
    private String mnemonicCode;
    
    /**
     * 医疗目录类别
     */
    private String projectType;
    
    /**
     * 医疗目录等级
     */
    private String projectLevel;
    
    /**
     * 注册规格
     */
    private String regSpec;
    
    /**
     * 规格
     */
    private String spec;
    
    /**
     * 注册剂型
     */
    private String regDosageForm;
    
    /**
     * 剂型
     */
    private String dosageForm;
    
    /**
     * 最小包装数量
     */
    private String minPackageNum;
    
    /**
     * 最小制剂单位
     */
    private String minPreparationUnit;
    
    /**
     * 最小包装单位
     */
    private String minPackageUnit;
    
    /**
     * 药品企业
     */
    private String pharmaceuticalEnterprise;
    
    /**
     * 生产企业
     */
    private String manufacturer;
    
    /**
     * 批准文号
     */
    private String approvalNumber;
    
    /**
     * 本位码
     */
    private String standardCode;



    /**
     * 是否禁用，默认否
     */
    private Boolean disable;

    /**
     * 备注
     */
    private String remark;

    /**
     * 扩展字段（JSON格式）
     */
    private MedicalCatalogDetailExtDTO ext;

}
