package com.xyy.saas.inquiry.product.api.product.dto;

import java.io.Serializable;
import java.time.LocalDateTime;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 标准库同步进度
 */
@Data
@Accessors(chain = true)
public class ProductStdlibSyncDto implements Serializable {

    @Schema(description = "编号")
    private Long id;

    @Schema(description = "任务ID")
    private String guid;

    @Schema(description = "同步类型 1-全量同步")
    private Integer type;

    @Schema(description = "同步状态 0-未开始 1-进行中 2-已完成 3-失败 4-已取消")
    private Integer status;

    @Schema(description = "开始ID")
    private Long startId;
    @Schema(description = "结束ID")
    private Long endId;

    @Schema(description = "当前进度")
    private Long current;

    @Schema(description = "开始时间")
    private LocalDateTime startTime;

    @Schema(description = "结束时间")
    private LocalDateTime endTime;

    @Schema(description = "错误信息")
    private String errorMsg;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "创建者")
    private String creator;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新者")
    private String updater;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;
} 