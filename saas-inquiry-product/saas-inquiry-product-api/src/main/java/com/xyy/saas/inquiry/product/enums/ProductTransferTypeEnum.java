package com.xyy.saas.inquiry.product.enums;

/**
 * desc 流转类型：上报中台、提报总部、分享、
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
public enum ProductTransferTypeEnum {

    // 上报中台
    REPORT_MID(1, "上报中台"),

    // 提报总部
    SUBMIT_HEAD(2, "提报总部"),

    // 分享商品
    SHARE(3, "分享商品"),



    ;

    public final int code;
    public final String desc;

    ProductTransferTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

}
