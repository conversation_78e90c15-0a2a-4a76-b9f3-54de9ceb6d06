package com.xyy.saas.inquiry.im.api.message.dto;


import java.io.Serializable;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * desc
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
@Data
@Accessors(chain = true)
public class NotificationPushConfigDto implements Serializable {

    /**
     * Android 通知配置
     */
    private AndroidChannelProperties androidChannelProperties;


    @Data
    public static class AndroidChannelProperties implements Serializable {

        /**
         * <pre>
         * Android app 的 channelId，需要与 app 中的 channelId 能对应上。
         * 设置 NotificationChannel 参数，具体用途请参见<a href="https://help.aliyun.com/document_detail/67398.html">常见问题：Android 8.0 以上设备通知接收不到</a>。
         * 因 OPPO 通知私信通道的 channel_id 与 app 的 channelId，故 OPPO 通道推送时，channel_id 取此值。
         * 华为、FCM、阿里云自有通道推送中，channel_id 取此值。
         * </pre>
         */
        private String androidNotificationChannel;

        /**
         * <pre>
         * 设置 Huawei 通知消息分类 importance 参数，决定用户设备消息通知行为，取值如下：
         * LOW：资讯营销类消息
         * NORMAL：服务与通讯类消息
         * 说明
         * 当前华为通道建议使用 AndroidMessageHuaweiCategory 进行通知分类，可不再使用 AndroidNotificationHuaweiChannel。
         * 需要在 Huawei 平台申请，<a href="https://developer.huawei.com/consumer/cn/doc/development/HMSCore-Guides/message-classification-0000001149358835#section893184112272">申请链接</a>。
         * </pre>
         * @deprecated 华为通道建议使用 AndroidMessageHuaweiCategory 进行通知分类
         */
        private String androidNotificationHuaweiChannel;
        /**
         * <pre>
         * 设置荣耀通知消息分类 importance 参数，决定用户设备消息通知行为，取值如下：
         * LOW：资讯营销类消息
         * NORMAL：服务与通讯类消息
         * 需要在荣耀平台申请，<a href="https://developer.honor.com/cn/docs/11002/guides/notification-class#%E8%87%AA%E5%88%86%E7%B1%BB%E6%9D%83%E7%9B%8A%E7%94%B3%E8%AF%B7">申请链接</a>。
         * </pre>
         */
        private String androidNotificationHonorChannel;
        /**
         * <pre>
         * 设置小米通知类型的 channelId，需要在小米平台申请，详见：<a href="https://dev.mi.com/console/doc/detail?pId=2422#_4">申请链接</a>。
         * 说明
         * 小米通道单个应用最多可申请 8 个 channel，请提前做好规划。
         * </pre>
         */
        private String androidNotificationXiaomiChannel;
        /**
         * <pre>
         * 设置 vivo 通知消息分类，取值为：
         * 0：运营类消息（默认）
         * 1：系统类消息
         * 说明
         * 当前 vivo 通道建议使用 AndroidMessageVivoCategory 进行通知分类，可不再使用 AndroidNotificationVivoChannel。
         * 需要在 vivo 平台申请，详见：<a href="https://dev.vivo.com.cn/documentCenter/doc/359">申请链接</a>。
         * </pre>
         * @deprecated vivo 通道建议使用 AndroidMessageVivoCategory 进行通知分类
         */
        private String androidNotificationVivoChannel;

        /**
         * <pre>
         * 消息分组，同一组消息在通知栏里只显示最新一条和当前该组接受到的消息总数目，不会展示所有消息也无法展开。当前支持：
         * 华为
         * 荣耀
         * 自有通道
         * </pre>
         */
        private String androidNotificationGroup;

        /**
         * <pre>
         * 作用一：完成<a href="https://developer.huawei.com/consumer/cn/doc/development/HMSCore-Guides/message-classification-0000001149358835?#section3410731125514">自分类权益申请</a>后，用于标识消息类型，确定<a href="https://developer.huawei.com/consumer/cn/doc/development/HMSCore-Guides/message-classification-0000001149358835#ZH-CN_TOPIC_0000001149358835__p3850133955718">消息提醒方式</a>，对特定类型消息加快发送，取值请参考华为推送官方文档的<a href="https://developer.huawei.com/consumer/cn/doc/development/HMSCore-Guides/message-classification-0000001149358835#section1076611477914">消息分类标准</a>，填写文档表格中的“云端通知 category 取值”或“本地通知 category 取值”。
         *
         * 作用二：<a href="https://developer.huawei.com/consumer/cn/doc/development/HMSCore-Guides/faq-0000001050042183#section037425218509">申请特殊权限</a>后，用于标识高优先级透传场景，取值如下：
         *
         * VOIP：音视频通话
         * PLAY_VOICE：语音播报
         * 说明
         * 对于“云端通知 category 取值”为“不涉及”的一概走阿里云自有通道，对于“本地通知 category 取值”为“不涉及”的一概走华为通道。
         * </pre>
         */
        private String androidMessageHuaweiCategory;
        /**
         * <pre>
         * OPPO 将消息分类：通讯与服务、内容与营销两个类别进行管理。
         *
         * 通讯与服务（需申请权限）：
         *
         * IM：即时聊天、音频、视频通话
         * ACCOUNT：个人账号与资产变化
         * DEVICE_REMINDER：个人设备提醒
         * ORDER：个人订单/物流状态变化
         * TODO：个人日程/待办
         * SUBSCRIPTION：个人订阅
         * 内容与营销：
         *
         * NEWS：新闻资讯
         * CONTENT：内容推荐
         * MARKETING：平台活动
         * SOCIAL：社交动态
         * 详细请参考 <a href="https://open.oppomobile.com/new/developmentDoc/info?id=13189">OPUSH 消息分类细则</a>
         * </pre>
         */
        private String androidMessageOppoCategory;
        /**
         * <pre>
         * vivo 将消息分为：系统消息、运营消息两个类别进行管理。 系统消息：
         *
         * IM：即时消息
         * ACCOUNT：账号与资产
         * TODO：日程待办
         * DEVICE_REMINDER：设备信息
         * ORDER：订单与物流
         * SUBSCRIPTION：订阅提醒
         * 运营消息：
         *
         * NEWS：新闻
         * CONTENT：内容推荐
         * MARKETING：运营活动
         * SOCIAL：社交动态
         * 详细请参考<a href="https://dev.vivo.com.cn/documentCenter/doc/359#s-ef3qugc3">分类说明</a>
         * </pre>
         */
        private String androidMessageVivoCategory;
    }
}
