package com.xyy.saas.inquiry.im.enums;

import com.xyy.saas.inquiry.im.api.message.dto.NotificationPushDto.OpenType;
import lombok.Getter;
import java.util.Map;

/**
 * @Author: xucao
 * @DateTime: 2025/5/26 12:00
 * @Description: 推送内容枚举 1、新订单提醒   2、抢单成功提醒   3、订单超时提示
 **/
@Getter
public enum PushContentEnum {
    NEW_ORDER("newOrder","新订单提醒","您有一个新的问诊订单，点击可立即抢单！", OpenType.H5,"/doctorWork?inquiryPref={0}&mobilePush=1"),
    GRAB_ORDER("grabOrder","抢单成功提醒","已为您自动抢单成功，请及时处理！", OpenType.H5,"/doctorCheck?inquiryPref={0}&mobilePush=1"),
    ORDER_TIMEOUT("orderTimeOut","订单超时提示","您的问诊订单即将超时关闭，请及时处理！", OpenType.H5,"/doctorCheck?inquiryPref={0}&mobilePush=1");

    private String code;
    private String title;
    private String body;
    private OpenType openType;
    private String pageUrl;

    PushContentEnum(String code ,String title, String body, OpenType openType, String pageUrl) {
        this.code = code;
        this.title = title;
        this.body = body;
        this.openType = openType;
        this.pageUrl = pageUrl;
    }
}
