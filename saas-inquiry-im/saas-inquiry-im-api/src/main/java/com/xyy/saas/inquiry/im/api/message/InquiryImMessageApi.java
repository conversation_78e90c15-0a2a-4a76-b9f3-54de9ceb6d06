package com.xyy.saas.inquiry.im.api.message;

import com.xyy.saas.inquiry.im.api.message.dto.FlushImHistoryDto;
import com.xyy.saas.inquiry.im.api.message.dto.InquiryImMessageDto;
import com.xyy.saas.inquiry.im.api.message.dto.InquiryLastMessageDto;
import java.util.List;

/**
 * @Author: xucao
 * @Date: 2024/11/28 15:19
 * @Description: IM消息相关接口
 */
public interface InquiryImMessageApi {

    /**
     * 发送消息(同步IM)
     *
     * @param messageDto 消息对象
     * @return boolean
     */
    Boolean sendUserMessage(InquiryImMessageDto messageDto);


    /**
     * 发送系统消息
     *
     * @param messageDto 消息对象
     * @return boolean
     */
    Boolean sendSystemMessage(InquiryImMessageDto messageDto);

    /**
     * 批量发送系统消息
     *
     * @param messageDto 消息对象
     * @return boolean
     */
    Boolean batchSendSystemMessage(InquiryImMessageDto messageDto);


    /**
     * 批量插入消息表（不同步IM）
     *
     * @param messageDtos 消息对象集合
     * @return 插入结果
     */
    Boolean batchInsertMessage(List<InquiryImMessageDto> messageDtos);


    /**
     * 批量获取问诊的最后一条消息
     *
     * @param inquiryPrefs 问诊单列表
     * @return 最后一条消息集合
     */
    List<InquiryLastMessageDto> getLastMessage(List<String> inquiryPrefs);

    /**
     * 批量更新问诊单im聊天记录
     *
     * @param flushImHistoryDtoList
     */
    Boolean batchUpdateInquiryImHistory(List<FlushImHistoryDto> flushImHistoryDtoList);

    /**
     * 获取问诊单的pdf文件
     *
     * @param inquiryPref
     * @return
     */
    String getImInquiryPdf(String inquiryPref);

}
