package com.xyy.saas.inquiry.im.enums;

import cn.iocoder.yudao.framework.common.exception.ErrorCode;

/**
 * 使用 2_003_000_000 段
 */
public interface ErrorCodeConstants {

    ErrorCode USER_NOT_EXISTS = new ErrorCode(2_003_000_000, "用户信息不存在");

    ErrorCode INQUIRY_IM_USER_NOT_EXISTS = new ErrorCode(2_003_000_001, "腾讯IM用户不存在");
    ErrorCode CREATE_TENCENT_IM_USER_ERROR = new ErrorCode(2_003_000_002, "创建腾讯IM用户失败");
    ErrorCode INQUIRY_IM_MESSAGE_NOT_EXISTS = new ErrorCode(2_003_000_003, "腾讯IM用户消息不存在");
    ErrorCode INQUIRY_IM_PDF_GENERATE_ERROR = new ErrorCode(2_003_000_004, "IM聊天记录生成失败");
    ErrorCode INQUIRY_IM_PDF_UPDATE_ERROR = new ErrorCode(2_003_000_005, "更新问诊单imPdf失败");
    ErrorCode INQUIRY_TRTC_CALL_BACK_MESSAGE_ERROR = new ErrorCode(2_003_000_006, "腾讯TRTC回调消息格式有误");
    ErrorCode INQUIRY_TRTC_PUSH_STREAM_ERROR = new ErrorCode(2_003_000_007, "当前问诊已推流");
    ErrorCode INQUIRY_TRTC_START_PUSH_STREAM_ERROR = new ErrorCode(2_003_000_008, "拉流转推失败");
    ErrorCode INQUIRY_TRTC_PUBLISH_STREAM_ERROR = new ErrorCode(2_003_000_009, "当前问诊已开启混流转推");
    ErrorCode INQUIRY_TRTC_START_PUBLISH_STREAM_ERROR = new ErrorCode(2_003_000_010, "开启混流转推失败");
    ErrorCode INQUIRY_TRTC_TRANSCODING_RECORD_NOT_EXISTS = new ErrorCode(2_003_000_011, "混流任务不存在");
    ErrorCode INQUIRY_TRTC_TRANSCODING_ERROR = new ErrorCode(2_003_000_012, "问诊视频混流转码失败");
    ErrorCode INQUIRY_IM_USER_PARAM_ERROR = new ErrorCode(2_003_000_013, "查询用户IM参数为空");
    ErrorCode INQUIRY_TRTC_SOURCE_STREAM_PULL_ERROR = new ErrorCode(2_003_000_014, "拉流转推前获取资源流视频失败");


    ErrorCode INQUIRY_ALI_OPEN_APP_KEY_NOT_EXISTS = new ErrorCode(2_003_000_021, "阿里开放平台AppKey不存在");
}


