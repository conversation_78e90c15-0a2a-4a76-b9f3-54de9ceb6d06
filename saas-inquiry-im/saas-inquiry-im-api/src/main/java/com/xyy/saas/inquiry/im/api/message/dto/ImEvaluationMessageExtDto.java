package com.xyy.saas.inquiry.im.api.message.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.io.Serializable;

/**
 * @Author: xucao
 * @Date: 2025/2/12 14:38
 * @Description: 问诊评价卡片消息扩展对象
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ImEvaluationMessageExtDto implements Serializable {
    private String inquiryPref;
    @Builder.Default
    private Boolean newInquiry = true;
    private String sourceType;
    private EvaluateInfo evaluateInfo;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class EvaluateInfo implements Serializable {
        private String name;
        private String titleName;
        private String hospital;
        private String office;
        private int status;
    }
}
