package com.xyy.saas.inquiry.im.api.message.dto;

import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Author: xucao
 * @DateTime: 2025/5/6 19:30
 * @Description: 气泡通知消息扩展信息
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ImNotifyBubbleMessageExtDto implements Serializable {

    private String inquiryPref;
    @Builder.Default
    private Boolean newInquiry = true;
    private String sourceType;
    private NotifyInfo notifyInfo;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class NotifyInfo implements Serializable {

        /**
         * 通知时间
         */
        private LocalDateTime notifyTime;

        private String notifyTimeStr;
    }
}
