package com.xyy.saas.inquiry.im.enums;


/**
 * desc
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
public enum DeviceTypeEnum {

    iOS(1), ANDROID(2), HARMONY(3)
    ;

    public final int code;

    DeviceTypeEnum(int code) {
        this.code = code;
    }

    public static DeviceTypeEnum fromCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (DeviceTypeEnum type : values()) {
            if (type.code == code) {
                return type;
            }
        }
        return null;
    }
}
