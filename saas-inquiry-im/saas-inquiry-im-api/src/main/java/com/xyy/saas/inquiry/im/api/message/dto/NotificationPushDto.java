package com.xyy.saas.inquiry.im.api.message.dto;


import java.io.Serializable;
import java.util.List;
import java.util.Map;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * desc
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class NotificationPushDto implements Serializable {

    /**
     * 推送账号列表
     */
    private List<Long> userIdList;


    /**
     * 通知标题。
     * 必需（当PushType=NOTICE时）。
     */
    private String title;

    /**
     * 通知/消息内容。
     * 必需。
     */
    private String body;

    /**
     * 自定义扩展参数（透传消息），JSON格式。
     * 可选。
     */
    private NotificationPushExtDto ext;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Accessors(chain = true)
    public static class NotificationPushExtDto implements Serializable {

        /**
         * 跳转动作
         */
        private Action action;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Accessors(chain = true)
    public static class Action implements Serializable {
        @JsonProperty("open_type")
        private OpenType openType;

        @JsonProperty("page_url")
        private String pageUrl;

        @JsonProperty("page_params")
        private Map<String, String> pageParams;
    }

    /**
     * 跳转类型枚举
     */
    public enum OpenType {
        @JsonProperty("NATIVE") NATIVE,
        @JsonProperty("H5") H5,
    }
}
