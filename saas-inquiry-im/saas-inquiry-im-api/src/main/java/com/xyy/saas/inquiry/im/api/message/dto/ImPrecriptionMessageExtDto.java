package com.xyy.saas.inquiry.im.api.message.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @Author: xucao
 * @Date: 2025/2/12 14:14
 * @Description: 处方卡片消息扩展对象模型
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ImPrecriptionMessageExtDto implements Serializable {

    private String inquiryPref;
    @Builder.Default
    private Boolean newInquiry = true;
    private String sourceType;
    private PrescriptionInfo prescriptionInfo;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class PrescriptionInfo implements Serializable {

        private String patientName;
        private int patientSex;
        private String patientAge;
        private String doctorName;
        private String deptName;
        private LocalDateTime outPrescriptionTime;
        private String outPrescriptionTimeStr;
        private LocalDateTime startTime;
        private String startTimeStr;
        private int medicationType;
        private String pref;
    }
}
