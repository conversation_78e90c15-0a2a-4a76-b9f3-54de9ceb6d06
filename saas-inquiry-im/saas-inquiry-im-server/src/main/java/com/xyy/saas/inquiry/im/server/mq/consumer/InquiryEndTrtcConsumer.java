package com.xyy.saas.inquiry.im.server.mq.consumer;

import cn.hutool.core.util.ObjectUtil;
import com.xyy.saas.eventbus.rocketmq.annotation.EventBusConsumer;
import com.xyy.saas.eventbus.rocketmq.annotation.EventBusListener;
import com.xyy.saas.inquiry.im.server.dal.dataobject.trtc.InquiryTranscodingDO;
import com.xyy.saas.inquiry.im.server.mq.message.InquiryTrtcEndEvent;
import com.xyy.saas.inquiry.im.server.mq.message.TranscodingTaskStartEvent;
import com.xyy.saas.inquiry.im.server.mq.message.dto.TranscodingTaskMessage;
import com.xyy.saas.inquiry.im.server.mq.producer.TranscodingTaskStartProducer;
import com.xyy.saas.inquiry.im.server.service.trtc.InquiryTranscodingService;
import com.xyy.saas.inquiry.mq.inquiry.InquiryEndEvent;
import com.xyy.saas.inquiry.mq.inquiry.dto.InquiryEndMessage;
import com.xyy.saas.inquiry.patient.api.inquiry.InquiryApi;
import com.xyy.saas.inquiry.patient.api.inquiry.dto.InquiryRecordDto;
import jakarta.annotation.Resource;
import java.time.LocalDateTime;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * @Author: xucao
 * @Date: 2024/12/11 11:56
 * @Description: 问诊结束事件，trtc侧响应处理 1、生成转码任务记录 2、发送转码MQ
 */
@Component
@Slf4j
@EventBusConsumer(groupId = "com_xyy_saas_inquiry_im_server_mq_consumer_InquiryTrtcEndConsumer",
    topic = InquiryTrtcEndEvent.TOPIC)
public class InquiryEndTrtcConsumer {

    @Resource
    private InquiryTranscodingService inquiryTranscodingService;

    @Resource
    private InquiryApi inquiryApi;

    @Resource
    private TranscodingTaskStartProducer transcodingTaskStartProducer;

    @EventBusListener
    public void onMessage(InquiryEndEvent inquiryEndEvent) {
        InquiryEndMessage msg = inquiryEndEvent.getMsg();
        // 获取问诊单信息
        InquiryRecordDto inquiryDto = inquiryApi.getInquiryDtoByPref(msg.getInquiryPref());
        // 非视频问诊不处理
        if (ObjectUtil.isEmpty(inquiryDto) || !inquiryDto.isVideoInquiry()) {
            return;
        }
        // 新增转码记录
        inquiryTranscodingService.addTranscodingRecord(InquiryTranscodingDO.builder().inquiryPref(inquiryDto.getPref()).build());
        // 发送转码延迟消息(5分钟后执行)
        transcodingTaskStartProducer.sendMessage(TranscodingTaskStartEvent.builder().msg(TranscodingTaskMessage.builder().InquiryPref(inquiryDto.getPref()).build()).build(), LocalDateTime.now().plusMinutes(5));
    }
}
