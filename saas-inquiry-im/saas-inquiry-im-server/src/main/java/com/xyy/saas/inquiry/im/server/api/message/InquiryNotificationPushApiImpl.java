package com.xyy.saas.inquiry.im.server.api.message;


import com.xyy.saas.inquiry.im.api.message.InquiryNotificationPushApi;
import com.xyy.saas.inquiry.im.api.message.dto.NotificationPushDto;
import com.xyy.saas.inquiry.im.server.service.push.NotificationPushService;
import jakarta.annotation.Resource;
import org.apache.dubbo.config.annotation.DubboService;

/**
 * desc
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
@DubboService
public class InquiryNotificationPushApiImpl implements InquiryNotificationPushApi {

    @Resource
    private NotificationPushService notificationPushService;


    @Override
    public void sendNoticeToMobile(NotificationPushDto dto) {
        notificationPushService.sendNoticeToMobile(dto);
    }
}
