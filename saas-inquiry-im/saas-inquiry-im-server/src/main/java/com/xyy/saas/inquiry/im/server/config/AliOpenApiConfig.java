package com.xyy.saas.inquiry.im.server.config;

import com.aliyun.teaopenapi.models.Config;
import com.xyy.saas.inquiry.im.api.message.dto.NotificationPushConfigDto.AndroidChannelProperties;
import com.xyy.saas.inquiry.im.enums.DeviceTypeEnum;
import lombok.Data;
import org.checkerframework.checker.units.qual.C;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.io.Serializable;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.xyy.saas.inquiry.im.enums.ErrorCodeConstants.INQUIRY_ALI_OPEN_APP_KEY_NOT_EXISTS;

/**
 * 阿里 EMAS 推送服务相关配置属性
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "ali.openapi")
public class AliOpenApiConfig {
    /**
     * 阿里云区域ID，例如 cn-hangzhou
     */
    private String regionId;

    /**
     * 阿里云 OpenAPI Endpoint，通常格式为 <RegionId>.aliyuncs.com
     */
    private String endpoint;

    /**
     * 阿里云 AccessKey ID
     */
    private String accessKeyId;

    /**
     * 阿里云 AccessKey Secret
     */
    private String accessKeySecret;


    private AliEmasPushConfig android;
    private AliEmasPushConfig ios;
    private AliEmasPushConfig harmony;

    /**
     * android 通道配置
     */
    private AndroidChannelProperties androidChannelProperties;

    /**
     * 转换为阿里云 OpenAPI 配置
     * @return
     */
    public Config toConfig() {
        Config config = new Config();
        config.accessKeyId = this.accessKeyId;
        config.accessKeySecret = this.accessKeySecret;
        config.endpoint = this.endpoint;
        config.regionId = this.regionId;
        return config;
    }

    /**
     * 根据设备类型获取AppKey
     * @param deviceTypeEnum
     * @return
     */
    public Long getAppKeyByDeviceType(DeviceTypeEnum deviceTypeEnum) {
        if (deviceTypeEnum == null) {
            return null;
        }
        AliEmasPushConfig config = switch (deviceTypeEnum) {
            case iOS -> ios;
            case ANDROID -> android;
            case HARMONY -> harmony;
        };
        if (config == null) {
            throw exception(INQUIRY_ALI_OPEN_APP_KEY_NOT_EXISTS);
        }
        return config.getAppKey();
    }




    @Data
    public static class AliEmasPushConfig implements Serializable {
        /**
         * EMAS 移动推送的 AppKey
         */
        private Long appKey;
        /**
         * iOS 的通知是通过 APNs 中心来发送的，需要填写对应的环境信息。'DEV': 表示开发环境 'PRODUCT': 表示生产环境
         */
        private String apnsEnv = "PRODUCT";

        /**
         * 是否开启离线消息推送，true 表示开启，false 表示关闭，默认为 true。
         */
        private Boolean storeOffline = true;
        /**
         * 离线消息保存时间，单位：秒，默认60s (不能小于3秒)
         */
        private Integer storeOfflineSeconds = 60;
        /**
         * 点击通知后动作
         * 'APPLICATION': 打开应用
         * 'ACTIVITY': 打开应用指定页面 AndroidActivity
         * 'URL': 打开 URL
         * 'NONE': 无跳转
         */
        private String androidOpenType = "APPLICATION";
        /**
         * 辅助弹窗页面activity
         * 使用厂商通道需要设置一个辅助弹窗，本插件写死辅助弹窗为com.alibaba.uniplugin.android.third.push.ThirdPushPopupActivity，效果为用户点击通知之后拉起应用。
         * 由于厂商通道由各厂商控制，消息点击回调仅在用户点击通知后触发。如果用户未点击或直接移除通知，应用将无法感知。
         */
        private String androidPopupActivity = "com.alibaba.uniplugin.android.third.push.ThirdPushPopupActivity";

        /**
         * 通知的提醒方式。可取值：
         * VIBRATE：振动（默认值）
         * SOUND：声音
         * BOTH：声音和振动
         * NONE：静音
         */
        private String androidNotifyType = "BOTH";
    }

}
