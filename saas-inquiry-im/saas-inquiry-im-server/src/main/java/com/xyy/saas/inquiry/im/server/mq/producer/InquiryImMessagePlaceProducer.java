package com.xyy.saas.inquiry.im.server.mq.producer;

import com.xyy.saas.eventbus.rocketmq.annotation.EventBusProducer;
import com.xyy.saas.eventbus.rocketmq.core.original.EventBusRocketMQTemplate;
import com.xyy.saas.inquiry.im.server.mq.message.InquiryImMessagePlaceEvent;
import org.springframework.stereotype.Component;

/**
 * @Author: xucao
 * @DateTime: 2025/4/15 21:53
 * @Description: 问诊结束IM消息归档事件生产者
 **/
@Component
@EventBusProducer(
    topic = InquiryImMessagePlaceEvent.TOPIC
)
public class InquiryImMessagePlaceProducer extends EventBusRocketMQTemplate {

}
