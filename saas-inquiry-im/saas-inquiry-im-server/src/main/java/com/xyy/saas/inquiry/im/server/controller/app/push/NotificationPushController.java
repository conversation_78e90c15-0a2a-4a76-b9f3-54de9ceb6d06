package com.xyy.saas.inquiry.im.server.controller.app.push;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import com.xyy.saas.inquiry.im.api.message.dto.NotificationPushConfigDto;
import com.xyy.saas.inquiry.im.api.message.dto.NotificationPushDto;
import com.xyy.saas.inquiry.im.server.service.push.NotificationPushService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 阿里云EMAS推送 Controller
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
@Tag(name = "APP - 阿里云EMAS推送")
@RestController
@RequestMapping("/im/ali-emas-push")
@Validated
@Slf4j
public class NotificationPushController {

    @Resource
    private NotificationPushService notificationPushService;

    @GetMapping("/config")
    @Operation(summary = "获取推送配置")
    public CommonResult<NotificationPushConfigDto> getConfig() {
        return success(notificationPushService.getConfig());
    }

    @PostMapping("/send-notice")
    @Operation(summary = "发送通知到手机")
    public CommonResult<Boolean> sendNoticeToMobile(@Valid @RequestBody NotificationPushDto pushReq) {
        log.info("发送通知到手机, 请求参数: {}", pushReq);
        notificationPushService.sendNoticeToMobile(pushReq);
        return success(true);
    }
}