package com.xyy.saas.inquiry.im.server.mq.consumer;

import com.alibaba.fastjson.JSON;
import com.xyy.saas.eventbus.rocketmq.annotation.EventBusConsumer;
import com.xyy.saas.eventbus.rocketmq.annotation.EventBusListener;
import com.xyy.saas.inquiry.im.server.controller.app.callback.vo.TencentImCallBackReqVO;
import com.xyy.saas.inquiry.im.server.mq.message.TencentImCallBackEvent;
import com.xyy.saas.inquiry.im.server.service.message.InquiryImMessageServiceImpl;
import com.xyy.saas.inquiry.im.server.service.message.strategy.imcallback.TencentImCallBackHandleStrategy;
import jakarta.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/**
 * @Author: xucao
 * @Date: 2024/11/29 13:42
 * {@link InquiryImMessageServiceImpl#onCallBackMessage(TencentImCallBackReqVO)}
 * @Description: 腾讯IM回调消息消费
 */
@Component
@Slf4j
@EventBusConsumer(groupId = "com_xyy_saas_inquiry_im_server_mq_consumer_TencentImCallBackConsumer",
        topic = TencentImCallBackEvent.TOPIC)
public class TencentImCallBackConsumer {

    /**
     * 回调处理策略
     */
    private static final Map<String, TencentImCallBackHandleStrategy> callBackHandleStrategyHashMap = new HashMap<>();

    @Resource
    public void initHandler(List<TencentImCallBackHandleStrategy> strategies){
        strategies.forEach(strategy-> callBackHandleStrategyHashMap.put(strategy.getEvent().getCode(),strategy));
    }

    @EventBusListener
    public void tencentImCallBackConsumer(TencentImCallBackEvent callBackEvent) {
        log.info("TencentImCallBackConsumer.onMessage,msg:{}", callBackEvent.getMsg());
        TencentImCallBackReqVO tencentImCallBackReqVO = JSON.parseObject(callBackEvent.getMsg(), TencentImCallBackReqVO.class);
        if(StringUtils.isBlank(tencentImCallBackReqVO.getCallbackCommand())){
            log.error("TencentImCallBackConsumer,CallbackCommand is null");
            return;
        }
        TencentImCallBackHandleStrategy strategy = (TencentImCallBackHandleStrategy) callBackHandleStrategyHashMap.get(tencentImCallBackReqVO.getCallbackCommand());
        //执行策略
        strategy.executeStrategy(tencentImCallBackReqVO);
    }
}
