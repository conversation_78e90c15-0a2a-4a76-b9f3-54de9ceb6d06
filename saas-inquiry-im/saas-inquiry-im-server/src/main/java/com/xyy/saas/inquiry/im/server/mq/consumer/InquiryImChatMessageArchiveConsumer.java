package com.xyy.saas.inquiry.im.server.mq.consumer;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.xyy.saas.inquiry.im.enums.ErrorCodeConstants.INQUIRY_IM_PDF_GENERATE_ERROR;
import static com.xyy.saas.inquiry.im.enums.ErrorCodeConstants.INQUIRY_IM_PDF_UPDATE_ERROR;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import com.google.common.collect.Lists;
import com.xyy.saas.eventbus.rocketmq.annotation.EventBusConsumer;
import com.xyy.saas.eventbus.rocketmq.annotation.EventBusListener;
import com.xyy.saas.inquiry.im.server.controller.app.message.vo.InquiryImMessagePageReqVO;
import com.xyy.saas.inquiry.im.server.controller.app.message.vo.InquiryImMessageRespVO;
import com.xyy.saas.inquiry.im.server.convert.message.InquiryImMessageConvert;
import com.xyy.saas.inquiry.im.server.mq.message.InquiryImMessagePlaceEvent;
import com.xyy.saas.inquiry.im.server.service.message.InquiryImMessageService;
import com.xyy.saas.inquiry.patient.api.inquiry.InquiryApi;
import com.xyy.saas.inquiry.patient.api.inquiry.dto.InquiryRecordDto;
import com.xyy.saas.inquiry.signature.api.immessage.InquiryImPdfApi;
import com.xyy.saas.inquiry.signature.api.immessage.dto.InquiryImMessageDto;
import jakarta.annotation.Resource;

import java.util.List;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

/**
 * @Author: xucao
 * @DateTime: 2025/4/15 21:55
 * @Description: IM消息归档事件消费者
 **/
@Component
@Slf4j
@EventBusConsumer(groupId = "com_xyy_saas_inquiry_im_server_mq_consumer_InquiryImChatMessageArchiveConsumer",
        topic = InquiryImMessagePlaceEvent.TOPIC)
public class InquiryImChatMessageArchiveConsumer {

    @Resource
    private InquiryImMessageService inquiryImMessageService;

    @Resource
    private InquiryImPdfApi inquiryImPdfApi;

    @Resource
    private InquiryApi inquiryApi;

    @EventBusListener
    public void onMessage(InquiryImMessagePlaceEvent event) {
        String inquiryPref = event.getMsg();
        if (StringUtils.isBlank(inquiryPref)) {
            return;
        }

        // step 1 获取问诊单信息
        InquiryRecordDto inquiryRecordDto = inquiryApi.getInquiryRecord(inquiryPref);

        // step 2 修改问诊单对应的im聊天记录文件
        inquiryImMessageService.batchUpdateInquiryImHistory(Lists.newArrayList(inquiryRecordDto));
    }
}
