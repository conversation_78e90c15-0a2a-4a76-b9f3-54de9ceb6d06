package com.xyy.saas.inquiry.im.server.dal.dataobject.tencent.in;

import com.alibaba.fastjson.annotation.JSONField;
import com.xyy.saas.inquiry.im.server.dal.dataobject.tencent.in.TencentImSendMessageDO.MsgBody;
import com.xyy.saas.inquiry.im.server.dal.dataobject.tencent.in.TencentImSendMessageDO.MsgBody.MsgContent;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import java.io.Serializable;
import java.util.List;

/**
 * @Author: xucao
 * @DateTime: 2025/4/7 20:14
 * @Description: 批量消息模型
 **/
@Data
@ToString(callSuper = true)
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class TencentImBatchMessageDO implements Serializable {

    @JSONField(name = "SyncOtherMachine")
    private Integer syncOtherMachine;

    @JSONField(name = "From_Account")
    private String fromAccount;

    @JSONField(name = "To_Account")
    private List<String> toAccount;

    @JSONField(name = "MsgRandom")
    private Long msgRandom;

    @JSONField(name = "ForbidCallbackControl")
    private List<String> forbidCallbackControl;

    @JSONField(name = "MsgSeq")
    private Long msgSeq;

    @JSONField(name = "MsgBody")
    private List<TencentImSendMessageDO.MsgBody> msgBody;

    @JSONField(name = "CloudCustomData")
    private String cloudCustomData;

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class MsgBody implements Serializable {

        @JSONField(name = "MsgType")
        private String msgType;

        @JSONField(name = "MsgContent")
        private TencentImSendMessageDO.MsgBody.MsgContent msgContent;


        @Data
        @Builder
        @AllArgsConstructor
        @NoArgsConstructor
        public static class MsgContent implements Serializable {

            @JSONField(name = "Text")
            private String text;
        }
    }

    @JSONField(name = "IsNeedReadReceipt")
    private Integer isNeedReadReceipt;
}
