package com.xyy.saas.inquiry.im.server.service.message;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import com.xyy.saas.inquiry.im.api.message.dto.InquiryImMessageDto;
import com.xyy.saas.inquiry.im.api.message.dto.InquiryLastMessageDto;
import com.xyy.saas.inquiry.im.server.controller.app.callback.vo.TencentImCallBackReqVO;
import com.xyy.saas.inquiry.im.server.controller.app.message.vo.InquiryImMessagePageReqVO;
import com.xyy.saas.inquiry.im.server.controller.app.message.vo.InquiryImMessageRespVO;
import com.xyy.saas.inquiry.im.server.controller.app.message.vo.InquiryImMessageSaveReqVO;
import com.xyy.saas.inquiry.im.server.dal.dataobject.message.InquiryImMessageDO;
import com.xyy.saas.inquiry.patient.api.inquiry.dto.InquiryRecordDto;
import jakarta.validation.Valid;

import java.util.List;

/**
 * @Author: xucao
 * @Date: 2024/11/28 16:06
 * @Description: IM消息服务
 */
public interface InquiryImMessageService {

    /**
     * 发送用户消息
     * @param messageDto 消息对象
     * @return boolean
     */
    Boolean sendUserMessage(InquiryImMessageDto messageDto);

    /**
     * 腾讯IM回调接口
     * @param tencentImCallBackReqVO 回调对象
     * @return boolean
     */
    Boolean onCallBackMessage(TencentImCallBackReqVO tencentImCallBackReqVO);

    /**
     * 创建腾讯IM用户消息
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createInquiryImMessage(@Valid InquiryImMessageSaveReqVO createReqVO);

    /**
     * 更新腾讯IM用户消息
     *
     * @param updateReqVO 更新信息
     */
    void updateInquiryImMessage(@Valid InquiryImMessageSaveReqVO updateReqVO);

    /**
     * 删除腾讯IM用户消息
     *
     * @param id 编号
     */
    void deleteInquiryImMessage(Long id);

    /**
     * 获得腾讯IM用户消息
     *
     * @param id 编号
     * @return 腾讯IM用户消息
     */
    InquiryImMessageDO getInquiryImMessage(Long id);

    /**
     * 获得腾讯IM用户消息分页
     *
     * @param pageReqVO 分页查询
     * @return 腾讯IM用户消息分页
     */
    PageResult<InquiryImMessageRespVO> getInquiryImMessagePage(InquiryImMessagePageReqVO pageReqVO);

    /**
     * IM回调阅读消息
     * @param callBackReqVO 回调对象
     */
    void readMsg(TencentImCallBackReqVO callBackReqVO);

    /**
     * 获得腾讯IM用户消息分页
     *
     * @param pageReqVO 查询条件
     * @return IM消息列表
     */
    List<InquiryImMessageRespVO> getInquiryImMessageList(InquiryImMessagePageReqVO pageReqVO);

    /**
     * 根据问诊单号删除聊天记录
     * @param inquiryPref 问诊单号
     */
    void deleteByInquiryPref(String inquiryPref);

    /**
     * 批量插入消息
     * @param messageDtos
     * @return
     */
    Boolean batchInsertMessage(List<InquiryImMessageDto> messageDtos);

    /**
     * 根据问诊单号批量获取最后一条消息
     * @param inquiryPrefList 问诊单号集合
     * @return 问诊单最后一条消息集合
     */
    List<InquiryLastMessageDto> getLastMessageByInquiryPrefs(List<String> inquiryPrefList);


    /**
     * 发送系统消息
     * @param messageDto
     * @return
     */
    Boolean sendSystemMessage(InquiryImMessageDto messageDto);

    /**
     * 批量发送系统消息
     * @param messageDto
     * @return
     */
    Boolean batchSendSystemMessage(InquiryImMessageDto messageDto);

    /**
     * 批量更新问诊单im聊天记录
     *
     * @param inquiryRecordDtoList
     */
    Boolean batchUpdateInquiryImHistory(List<InquiryRecordDto> inquiryRecordDtoList);
}
