package com.xyy.saas.inquiry.im.server.api.trtc;

import com.xyy.saas.inquiry.im.api.trtc.InquiryTrtcApi;
import com.xyy.saas.inquiry.im.server.service.trtc.InquiryTrtcService;
import jakarta.annotation.Resource;
import org.apache.dubbo.config.annotation.DubboService;

/**
 * @Author: xucao
 * @DateTime: 2025/7/21 13:51
 * @Description: TRTC接口实现类
 **/
@DubboService
public class InquiryTrtcApiImpl implements InquiryTrtcApi {

    @Resource
    private InquiryTrtcService inquiryTrtcService;

    /**
     * 解散直播间
     *
     * @param roomId
     */
    @Override
    public void destroyRoom(String roomId) {
        inquiryTrtcService.destroyRoom(roomId);
    }
}
