package com.xyy.saas.inquiry.im.server.mq.producer;

import com.xyy.saas.eventbus.rocketmq.annotation.EventBusProducer;
import com.xyy.saas.eventbus.rocketmq.core.original.EventBusRocketMQTemplate;
import com.xyy.saas.inquiry.im.server.mq.message.TranscodingTaskEndEvent;
import org.springframework.stereotype.Component;

/**
 * @Author: xucao
 * @Date: 2024/12/15 17:23
 * @Description: 转码结束消息生产者
 */
@Component
@EventBusProducer(
    topic = TranscodingTaskEndEvent.TOPIC
)
public class TranscodingTaskEndProducer extends EventBusRocketMQTemplate {

}
