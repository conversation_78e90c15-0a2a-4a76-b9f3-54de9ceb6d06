package com.xyy.saas.inquiry.im.server.service.message.strategy.imcallback;

import com.xyy.saas.inquiry.enums.tencent.TencentImCallBackEventEnum;
import com.xyy.saas.inquiry.im.server.controller.app.callback.vo.TencentImCallBackReqVO;

/**
 * @Author: xucao
 * @Date: 2024/11/29 13:57
 * @Description: 腾讯IM回调处理策略
 */
public interface TencentImCallBackHandleStrategy {

    /**
     * 策略执行器
     * @param callBackReqVO 回调参数
     */
    void executeStrategy(TencentImCallBackReqVO callBackReqVO);

    /**
     * 获取策略对应的事件
     * @return 事件
     */
    TencentImCallBackEventEnum getEvent();
}
