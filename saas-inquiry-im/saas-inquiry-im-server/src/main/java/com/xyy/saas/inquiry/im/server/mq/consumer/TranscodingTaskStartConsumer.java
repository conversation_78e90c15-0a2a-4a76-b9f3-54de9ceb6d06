package com.xyy.saas.inquiry.im.server.mq.consumer;

import com.xyy.saas.eventbus.rocketmq.annotation.EventBusConsumer;
import com.xyy.saas.eventbus.rocketmq.annotation.EventBusListener;
import com.xyy.saas.inquiry.enums.inquiry.TranscodingStatusEnum;
import com.xyy.saas.inquiry.im.server.dal.dataobject.tencent.out.TencentTrtcBaseRespDO;
import com.xyy.saas.inquiry.im.server.dal.dataobject.trtc.InquiryTranscodingDO;
import com.xyy.saas.inquiry.im.server.mq.message.TranscodingTaskEndEvent;
import com.xyy.saas.inquiry.im.server.mq.message.TranscodingTaskStartEvent;
import com.xyy.saas.inquiry.im.server.mq.message.dto.TranscodingTaskMessage;
import com.xyy.saas.inquiry.im.server.mq.producer.TranscodingTaskEndProducer;
import com.xyy.saas.inquiry.im.server.service.tencent.TencentTrtcClient;
import com.xyy.saas.inquiry.im.server.service.trtc.InquiryTranscodingService;
import com.xyy.saas.inquiry.patient.api.inquiry.InquiryApi;
import com.xyy.saas.inquiry.patient.api.inquiry.dto.InquiryRecordDto;
import jakarta.annotation.Resource;
import java.time.LocalDateTime;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/**
 * @Author: xucao
 * @Date: 2024/12/11 15:35
 * @Description: 转码任务开始消息消费
 */
@Component
@Slf4j
@EventBusConsumer(groupId = "com_xyy_saas_inquiry_im_server_mq_consumer_TranscodingTaskStartConsumer",
    topic = TranscodingTaskStartEvent.TOPIC)
public class TranscodingTaskStartConsumer {

    @Resource
    private TencentTrtcClient tencentTrtcClient;

    @Resource
    private InquiryApi inquiryApi;

    @Resource
    private InquiryTranscodingService inquiryTranscodingService;

    @Resource
    private TranscodingTaskEndProducer transCodingTaskEndProducer;

    @EventBusListener
    public void onMessage(TranscodingTaskStartEvent event) {
        TranscodingTaskMessage msg = event.getMsg();
        //获取问诊单信息
        InquiryRecordDto inquiryDto = inquiryApi.getInquiryRecord(msg.getInquiryPref());
        InquiryTranscodingDO transcodingDO = inquiryTranscodingService.queryByInquiryPref(msg.getInquiryPref());
        //转码任务为空、或已开启过转码、直接ack消息
        if(transcodingDO.getStatus() >= TranscodingStatusEnum.TRANSCODING_STARTED.getCode()){
            return;
        }
        //查找文件id
        TencentTrtcBaseRespDO fileResp = tencentTrtcClient.getFileIdByStreamId(inquiryDto.getStreamId());
        //失败时记录
        if(StringUtils.isBlank(fileResp.getFileId())){
            inquiryTranscodingService.updateTranscodingRecord(transcodingDO, fileResp, TranscodingStatusEnum.FILE_ID_NOT_FOUND);
        }
        //开启转码
        TencentTrtcBaseRespDO respDO = tencentTrtcClient.openTranscoding(fileResp.getFileId());
        if(StringUtils.isBlank(respDO.getTaskId())){
            inquiryTranscodingService.updateTranscodingRecord(transcodingDO, respDO, TranscodingStatusEnum.TRANSCODING_START_FAILED);
        }
        //记录转码成功
        inquiryTranscodingService.updateTranscodingRecord(transcodingDO, respDO, TranscodingStatusEnum.TRANSCODING_STARTED);
        //发送延迟消息进行转存储，延迟20分钟执行
        transCodingTaskEndProducer.sendMessage(TranscodingTaskEndEvent.builder().msg(TranscodingTaskMessage.builder().InquiryPref(inquiryDto.getPref()).taskId(respDO.getTaskId()).build()).build(), LocalDateTime.now().plusMinutes(20));
    }
}
