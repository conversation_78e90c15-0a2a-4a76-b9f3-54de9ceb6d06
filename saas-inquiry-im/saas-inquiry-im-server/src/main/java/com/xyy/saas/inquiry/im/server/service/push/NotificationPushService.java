package com.xyy.saas.inquiry.im.server.service.push;


import com.xyy.saas.inquiry.im.api.message.dto.NotificationPushConfigDto;
import com.xyy.saas.inquiry.im.api.message.dto.NotificationPushDto;

/**
 * desc
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
public interface NotificationPushService {

    /**
     * 获取配置
     *
     */
    NotificationPushConfigDto getConfig();

    /**
     * 发送通知到手机
     * @param pushReq
     */
    void sendNoticeToMobile(NotificationPushDto pushReq);


}
