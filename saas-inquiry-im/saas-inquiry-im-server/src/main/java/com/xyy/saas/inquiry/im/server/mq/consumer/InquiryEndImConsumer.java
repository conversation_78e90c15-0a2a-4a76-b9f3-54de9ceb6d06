package com.xyy.saas.inquiry.im.server.mq.consumer;

import cn.iocoder.yudao.module.infra.api.config.ConfigApi;
import com.xyy.saas.eventbus.rocketmq.annotation.EventBusConsumer;
import com.xyy.saas.eventbus.rocketmq.annotation.EventBusListener;
import com.xyy.saas.inquiry.constant.InquiryConstant;
import com.xyy.saas.inquiry.im.server.mq.message.InquiryImMessagePlaceEvent;
import com.xyy.saas.inquiry.im.server.mq.producer.InquiryImMessagePlaceProducer;
import com.xyy.saas.inquiry.mq.inquiry.InquiryEndEvent;
import com.xyy.saas.inquiry.mq.inquiry.dto.InquiryEndMessage;
import com.xyy.saas.inquiry.patient.api.inquiry.InquiryApi;
import com.xyy.saas.inquiry.patient.api.inquiry.dto.InquiryRecordDto;
import com.xyy.saas.inquiry.util.MathUtil;
import jakarta.annotation.Resource;
import java.time.LocalDateTime;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * @Author: xucao
 * @Date: 2024/12/03 15:03 {@link com.xyy.saas.inquiry.hospital.server.mq.producer.inquiry.InquiryEndProducer}
 * @Description: 问诊结束事件，im侧响应处理 1、组装消息列表 2、调签章服务，生成PDF文件 3、调医院服务，更新处方信息 4、根据问诊单号、删除聊天记录
 */
@Component
@Slf4j
@EventBusConsumer(groupId = "com_xyy_saas_inquiry_im_server_mq_consumer_InquiryEndImConsumer",
    topic = InquiryEndEvent.TOPIC)
public class InquiryEndImConsumer {

    @Resource
    private InquiryApi inquiryApi;

    @Resource
    private InquiryImMessagePlaceProducer inquiryImMessagePlaceProducer;

    @Resource
    private ConfigApi configApi;


    @EventBusListener
    public void onMessage(InquiryEndEvent inquiryEndEvent) {
        InquiryEndMessage message = inquiryEndEvent.getMsg();
        // 获取问诊单信息
        InquiryRecordDto inquiryDto = inquiryApi.getInquiryRecord(message.getInquiryPref());
        //非图文问诊不处理
        if(!inquiryDto.isTextInquiry()){
            return;
        }
        // 发送延迟消息归档
        inquiryImMessagePlaceProducer.sendMessage(InquiryImMessagePlaceEvent.builder().msg(inquiryDto.getPref()).build(), LocalDateTime.now().plusMinutes(
            MathUtil.formatNumberWithDefault(configApi.getConfigValueByKey(InquiryConstant.INQUIRY_IM_PLACE),5)));
    }
}
