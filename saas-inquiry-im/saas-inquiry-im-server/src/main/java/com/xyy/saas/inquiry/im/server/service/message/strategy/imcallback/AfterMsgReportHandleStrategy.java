package com.xyy.saas.inquiry.im.server.service.message.strategy.imcallback;

import com.xyy.saas.inquiry.enums.tencent.TencentImCallBackEventEnum;
import com.xyy.saas.inquiry.im.server.controller.app.callback.vo.TencentImCallBackReqVO;
import com.xyy.saas.inquiry.im.server.service.message.InquiryImMessageService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

/**
 * @Author: xucao
 * @Date: 2024/11/29 14:15
 * @Description: IM消息已读上报回调事件处理策略
 */
@Component
public class AfterMsgReportHandleStrategy implements TencentImCallBackHandleStrategy{

    @Resource
    private InquiryImMessageService inquiryImMessageService;

    /**
     * 策略执行器
     *
     * @param callBackReqVO 回调参数
     */
    @Override
    public void executeStrategy(TencentImCallBackReqVO callBackReqVO) {
        //根据时间批量已读消息
        inquiryImMessageService.readMsg(callBackReqVO);
    }

    /**
     * 获取策略对应的事件
     *
     * @return 事件
     */
    @Override
    public TencentImCallBackEventEnum getEvent() {
        return TencentImCallBackEventEnum.C2C_CALLBACK_AFTER_MSG_REPORT;
    }
}
