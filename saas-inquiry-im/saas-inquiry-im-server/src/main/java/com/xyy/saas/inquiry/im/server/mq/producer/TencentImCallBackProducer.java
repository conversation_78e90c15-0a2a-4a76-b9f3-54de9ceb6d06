package com.xyy.saas.inquiry.im.server.mq.producer;

import com.xyy.saas.eventbus.rocketmq.annotation.EventBusProducer;
import com.xyy.saas.eventbus.rocketmq.core.original.EventBusRocketMQTemplate;
import com.xyy.saas.inquiry.im.server.mq.message.TencentImCallBackEvent;
import org.springframework.stereotype.Component;

/**
 * @Author: xucao
 * @Date: 2024/11/29 13:40
 * @Description: 腾讯IM回调消息生产者
 */
@Component
@EventBusProducer(
        topic = TencentImCallBackEvent.TOPIC
)
public class TencentImCallBackProducer extends EventBusRocketMQTemplate {
}
