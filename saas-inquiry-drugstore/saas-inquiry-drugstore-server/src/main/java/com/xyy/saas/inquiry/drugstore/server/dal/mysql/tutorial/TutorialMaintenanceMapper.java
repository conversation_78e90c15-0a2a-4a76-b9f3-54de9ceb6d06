package com.xyy.saas.inquiry.drugstore.server.dal.mysql.tutorial;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.xyy.saas.inquiry.drugstore.server.controller.admin.tutorial.vo.TutorialMaintenancePageReqVO;
import com.xyy.saas.inquiry.drugstore.server.dal.dataobject.tutorial.TutorialMaintenanceDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 教程维护 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface TutorialMaintenanceMapper extends BaseMapperX<TutorialMaintenanceDO> {

    default PageResult<TutorialMaintenanceDO> selectPage(TutorialMaintenancePageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<TutorialMaintenanceDO>()
            .eqIfPresent(TutorialMaintenanceDO::getBizType, reqVO.getBizType())
            .eqIfPresent(TutorialMaintenanceDO::getTutorialType, reqVO.getTutorialType())
            .eqIfPresent(TutorialMaintenanceDO::getTutorialUrl, reqVO.getTutorialUrl())
            .eqIfPresent(TutorialMaintenanceDO::getTitle, reqVO.getTitle())
            .eqIfPresent(TutorialMaintenanceDO::getContent, reqVO.getContent())
            .betweenIfPresent(TutorialMaintenanceDO::getCreateTime, reqVO.getCreateTime())
            .orderByDesc(TutorialMaintenanceDO::getId));
    }

}