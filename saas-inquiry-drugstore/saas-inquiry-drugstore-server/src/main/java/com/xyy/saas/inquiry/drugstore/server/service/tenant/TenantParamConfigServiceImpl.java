package com.xyy.saas.inquiry.drugstore.server.service.tenant;

import static cn.iocoder.yudao.module.system.enums.LogRecordConstants.SYSTEM_TENANT_PARAM_CONFIG_UPDATE_SUB_TYPE;
import static cn.iocoder.yudao.module.system.enums.LogRecordConstants.SYSTEM_TENANT_PARAM_CONFIG_UPDATE_SUCCESS;
import static cn.iocoder.yudao.module.system.enums.LogRecordConstants.SYSTEM_TENANT_TYPE;
import static com.xyy.saas.inquiry.drugstore.enums.ErrorCodeConstants.TENANT_PARAM_CONFIG_NOT_EXISTS;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.framework.tenant.core.aop.TenantIgnore;
import cn.iocoder.yudao.framework.tenant.core.context.TenantContextHolder;
import cn.iocoder.yudao.framework.tenant.core.util.TenantUtils;
import com.alibaba.fastjson.JSON;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.starter.annotation.LogRecord;
import com.xyy.saas.inquiry.constant.TenantConstant;
import com.xyy.saas.inquiry.drugstore.mq.message.TenantParamConfigUpdateEvent;
import com.xyy.saas.inquiry.drugstore.server.controller.admin.drugstore.vo.DrugStoreParamConfigRespVO;
import com.xyy.saas.inquiry.drugstore.server.controller.admin.drugstore.vo.TenantParamConfigSaveReqVO;
import com.xyy.saas.inquiry.drugstore.server.convert.tennat.TenantParamConfigConvert;
import com.xyy.saas.inquiry.drugstore.server.dal.dataobject.tenant.TenantParamConfigDO;
import com.xyy.saas.inquiry.drugstore.server.dal.mysql.tenant.TenantParamConfigMapper;
import com.xyy.saas.inquiry.drugstore.server.dal.redis.RedisKeyConstants;
import com.xyy.saas.inquiry.drugstore.server.mq.producer.TenantParamConfigUpdateProducer;
import com.xyy.saas.inquiry.enums.tenant.TenantParamConfigTypeEnum;
import jakarta.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

/**
 * 门店参数配置 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class TenantParamConfigServiceImpl implements TenantParamConfigService {

    @Resource
    private TenantParamConfigMapper tenantParamConfigMapper;
    @Resource
    private TenantPackageCostService tenantPackageCostService;

    @Resource
    private TenantParamConfigUpdateProducer tenantParamConfigUpdateProducer;

    private TenantParamConfigServiceImpl getSelf() {
        return SpringUtil.getBean(getClass());
    }

    @Override
    public void saveOrUpdateTenantParamConfig(TenantParamConfigSaveReqVO saveReqVO) {
        // 查询 新增 或 更新
        TenantParamConfigDO tenantParamConfigDO = tenantParamConfigMapper.queryOneByCondition(saveReqVO);
        if (tenantParamConfigDO == null) {
            getSelf().createTenantParamConfig(saveReqVO);
        } else {
            getSelf().updateTenantParamConfig(saveReqVO);
        }
    }

    @Override
//    @LogRecord(type = SYSTEM_TENANT_TYPE, subType = SYSTEM_TENANT_PARAM_CONFIG_CREATE_SUB_TYPE, bizNo = "{{#createReqVO.tenantId}}",
//            success = SYSTEM_TENANT_PARAM_CONFIG_CREATE_SUCCESS)
    public Long createTenantParamConfig(TenantParamConfigSaveReqVO createReqVO) {
        // 插入
        TenantParamConfigDO tenantParamConfig = BeanUtils.toBean(createReqVO, TenantParamConfigDO.class);
        tenantParamConfigMapper.insert(tenantParamConfig);

        // 记录操作日志上下文
        // LogRecordContext.putVariable("paramConfig", tenantParamConfig);
        // 返回
        return tenantParamConfig.getId();
    }

    @Override
    @LogRecord(type = SYSTEM_TENANT_TYPE, subType = SYSTEM_TENANT_PARAM_CONFIG_UPDATE_SUB_TYPE, bizNo = "{{#paramConfig.tenantId}}",
        success = SYSTEM_TENANT_PARAM_CONFIG_UPDATE_SUCCESS, condition = "{{#condition}}")
    @CacheEvict(cacheNames = RedisKeyConstants.DRUGSTORE_PARAM_CONFIG, key = "#updateReqVO.tenantId+'_'+#updateReqVO.paramType")
    public void updateTenantParamConfig(TenantParamConfigSaveReqVO updateReqVO) {
        TenantParamConfigDO tenantParamConfigDO = tenantParamConfigMapper.queryOneByCondition(updateReqVO);
        // 更新
        TenantParamConfigDO updateObj = BeanUtils.toBean(updateReqVO, TenantParamConfigDO.class);
        tenantParamConfigMapper.updateByParamType(updateObj);
        // 记录操作日志上下文
        LogRecordContext.putVariable("paramConfig", tenantParamConfigDO);
        LogRecordContext.putVariable("updateReqVO", updateReqVO);
        LogRecordContext.putVariable("condition", !StringUtils.equals(tenantParamConfigDO.getParamValue(), updateReqVO.getParamValue()));

        // 门店配置变更MQ
        tenantParamConfigUpdateProducer.sendMessage(TenantParamConfigUpdateEvent.builder().msg(TenantParamConfigConvert.INSTANCE.convertDto(tenantParamConfigDO)).build(), LocalDateTime.now().plusSeconds(1));
    }

    @Override
    public void deleteTenantParamConfig(Long id) {
        // 校验存在
        validateTenantParamConfigExists(id);
        // 删除
        tenantParamConfigMapper.deleteById(id);
    }

    private void validateTenantParamConfigExists(Long id) {
        if (tenantParamConfigMapper.selectById(id) == null) {
            throw ServiceExceptionUtil.exception(TENANT_PARAM_CONFIG_NOT_EXISTS);
        }
    }

    // *********************** 基础参数获取 start *********************


    @Override
    @TenantIgnore
    public DrugStoreParamConfigRespVO getDrugStoreParamConfig(Long id) {
        List<TenantParamConfigDO> tenantParamConfigDOS = tenantParamConfigMapper.selectByTenantId(Optional.ofNullable(id).orElse(TenantContextHolder.getRequiredTenantId()));

        Map<String, String> paramMap = tenantParamConfigDOS.stream().peek(p -> p.setParamName(TenantParamConfigTypeEnum.fromType(p.getParamType()).getField()))
            .collect(Collectors.toMap(TenantParamConfigDO::getParamName, TenantParamConfigDO::getParamValue));

        DrugStoreParamConfigRespVO configRespVO = JSON.parseObject(JSON.toJSONString(paramMap), DrugStoreParamConfigRespVO.class);
        configRespVO.setPlatformReview(TenantUtils.execute(Optional.ofNullable(id).orElse(TenantContextHolder.getRequiredTenantId()), () -> tenantPackageCostService.validPlatformReviewCost()));

        tenantParamConfigDOS.stream().filter(p -> Objects.equals(TenantParamConfigTypeEnum.PRESCRIPTION_REMOTE_TO_HEAD_SWITCH.getType(), p.getParamType()))
            .findFirst().ifPresent(p -> {
                configRespVO.setPrescriptionRemoteToHeadTime(p.extGet().getPrescriptionRemoteToHeadTime());
            });
        return configRespVO;
    }

    @Override
    public TenantParamConfigDO getTenantParamConfig(Long id) {
        return tenantParamConfigMapper.selectById(id);
    }


    @Override
    public TenantParamConfigDO getTenantParamConfig(TenantParamConfigTypeEnum tenantParamConfigTypeEnum) {
        return tenantParamConfigMapper.selectOne(TenantParamConfigDO::getParamType, tenantParamConfigTypeEnum.getType());
    }

    @Override
    public String getTenantParamConfigValue(TenantParamConfigTypeEnum tenantParamConfigTypeEnum) {
        TenantParamConfigDO paramConfigDO = tenantParamConfigMapper.selectOne(TenantParamConfigDO::getParamType, tenantParamConfigTypeEnum.getType());
        if (paramConfigDO == null) {
            return null;
        }
        return paramConfigDO.getParamValue();
    }

    @Override
    @TenantIgnore
    public TenantParamConfigDO getTenantParamConfig(Long tenantId, TenantParamConfigTypeEnum tenantParamConfigTypeEnum) {
        return tenantParamConfigMapper.selectByType(tenantId, tenantParamConfigTypeEnum.getType());
    }

    @Override
    @TenantIgnore
    public String getTenantParamConfigValue(Long tenantId, TenantParamConfigTypeEnum tenantParamConfigTypeEnum) {
        TenantParamConfigDO paramConfigDO = tenantParamConfigMapper.selectByType(tenantId, tenantParamConfigTypeEnum.getType());
        if (paramConfigDO == null) {
            return null;
        }
        return paramConfigDO.getParamValue();
    }

    @Override
    @TenantIgnore
    public List<TenantParamConfigDO> getTenantParamConfig(Long tenantId, List<TenantParamConfigTypeEnum> tenantParamConfigTypeEnums) {
        if (CollUtil.isEmpty(tenantParamConfigTypeEnums)) {
            return List.of();
        }
        return tenantParamConfigMapper.selectByTypes(Collections.singletonList(tenantId), tenantParamConfigTypeEnums.stream().map(TenantParamConfigTypeEnum::getType).collect(Collectors.toSet()));
    }

    @Override
    @TenantIgnore
    public Map<Integer, TenantParamConfigDO> getTenantParamConfigMap(Long tenantId, List<TenantParamConfigTypeEnum> tenantParamConfigTypeEnums) {
        List<TenantParamConfigDO> paramConfig = getTenantParamConfig(tenantId, tenantParamConfigTypeEnums);
        return paramConfig.stream().collect(Collectors.toMap(TenantParamConfigDO::getParamType, Function.identity(), (a, b) -> b));
    }

    @Override
    @TenantIgnore
    public List<TenantParamConfigDO> getTenantParamConfig(List<Long> tenantIds, TenantParamConfigTypeEnum tenantParamConfigTypeEnum) {
        return tenantParamConfigMapper.selectByTenantIds(tenantIds, tenantParamConfigTypeEnum);
    }

    @Override
    @TenantIgnore
    public Map<Long, Map<Integer, TenantParamConfigDO>> getTenantParamConfig(List<Long> tenantIds, List<TenantParamConfigTypeEnum> tenantParamConfigTypeEnums) {
        if (CollUtil.isEmpty(tenantIds) || CollUtil.isEmpty(tenantParamConfigTypeEnums)) {
            return Map.of();
        }
        List<TenantParamConfigDO> tenantParamConfigDOS = tenantParamConfigMapper.selectByTypes(tenantIds, tenantParamConfigTypeEnums.stream().map(TenantParamConfigTypeEnum::getType).collect(Collectors.toSet()));
        return tenantParamConfigDOS.stream().collect(Collectors.groupingBy(TenantParamConfigDO::getTenantId, Collectors.toMap(TenantParamConfigDO::getParamType, Function.identity(), (a, b) -> b)));
    }

    // *********************** 基础参数获取 end *********************

    // *********************** 问诊参数获取 start  部分优先全局设置 后 门店设置 *********************

    @Override
    @TenantIgnore
    public String getParamConfigValueSelf2Sys(TenantParamConfigTypeEnum tenantParamConfigTypeEnum) {
        return getSelf().getParamConfigValueSelf2SysBase(TenantContextHolder.getRequiredTenantId(), tenantParamConfigTypeEnum.getType());
    }

    @Override
    public Integer getParamConfigValueSelf2SysInteger(TenantParamConfigTypeEnum tenantParamConfigTypeEnum) {
        String configValue = getSelf().getParamConfigValueSelf2SysBase(TenantContextHolder.getRequiredTenantId(), tenantParamConfigTypeEnum.getType());
        return NumberUtils.isDigits(configValue) ? NumberUtils.toInt(configValue) : null;
    }

    @Cacheable(cacheNames = RedisKeyConstants.DRUGSTORE_PARAM_CONFIG + "#60s", key = "#tenantId+'_'+#type")
    public String getParamConfigValueSelf2SysBase(Long tenantId, Integer type) {
        TenantParamConfigDO paramConfigDO = tenantParamConfigMapper.selectByType(tenantId, type);
        if (paramConfigDO != null) {
            return paramConfigDO.getParamValue();
        }
        TenantParamConfigDO sysParamConfigDO = tenantParamConfigMapper.selectByType(TenantConstant.DEFAULT_TENANT_ID, type);
        if (sysParamConfigDO != null) {
            return sysParamConfigDO.getParamValue();
        }
        return "";
    }


    @Override
    public String getParamConfigValueSys2Self(TenantParamConfigTypeEnum tenantParamConfigTypeEnum) {
        return getSelf().getParamConfigValueSys2SelfBase(TenantContextHolder.getRequiredTenantId(), tenantParamConfigTypeEnum.getType());
    }

    @Override
    public Integer getParamConfigValueSys2SelfInteger(TenantParamConfigTypeEnum tenantParamConfigTypeEnum) {
        String configValue = getSelf().getParamConfigValueSys2SelfBase(TenantContextHolder.getRequiredTenantId(), tenantParamConfigTypeEnum.getType());
        return NumberUtils.isDigits(configValue) ? NumberUtils.toInt(configValue) : null;
    }

    @Cacheable(cacheNames = RedisKeyConstants.DRUGSTORE_PARAM_CONFIG + "#60s", key = "#tenantId+'_'+#type")
    public String getParamConfigValueSys2SelfBase(Long tenantId, Integer type) {
        TenantParamConfigDO sysParamConfigDO = tenantParamConfigMapper.selectByType(TenantConstant.DEFAULT_TENANT_ID, type);
        if (sysParamConfigDO != null) {
            return sysParamConfigDO.getParamValue();
        }

        TenantParamConfigDO paramConfigDO = tenantParamConfigMapper.selectByType(tenantId, type);
        if (paramConfigDO != null) {
            return paramConfigDO.getParamValue();
        }
        return "";
    }

    // *********************** 问诊参数获取 end  部分优先全局设置 后 门店设置 *********************


}