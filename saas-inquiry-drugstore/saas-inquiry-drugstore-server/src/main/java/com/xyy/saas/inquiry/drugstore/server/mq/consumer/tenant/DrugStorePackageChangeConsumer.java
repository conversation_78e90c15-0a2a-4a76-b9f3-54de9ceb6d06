package com.xyy.saas.inquiry.drugstore.server.mq.consumer.tenant;

import com.xyy.saas.eventbus.rocketmq.annotation.EventBusConsumer;
import com.xyy.saas.eventbus.rocketmq.annotation.EventBusListener;
import com.xyy.saas.inquiry.drugstore.server.convert.tennat.TenantPackageCostConvert;
import com.xyy.saas.inquiry.drugstore.server.service.tenant.TenantPackageCostService;
import com.xyy.saas.inquiry.mq.tenant.TenantPackageChangeEvent;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> {@link cn.iocoder.yudao.module.system.service.tenant.TenantPackageRelationServiceImpl#createTenantPackageRelation}
 */
@Component
@Slf4j
@EventBusConsumer(groupId = "com_xyy_saas_inquiry_drugstore_server_mq_consumer_tenant_DrugStorePackageCostConsumer",
    topic = TenantPackageChangeEvent.TOPIC)
public class DrugStorePackageChangeConsumer {

    public static final String GROUP_ID = DrugStorePackageChangeConsumer.class.getName();

    @Resource
    private TenantPackageCostService tenantPackageCostService;

    /**
     * change
     *
     * @param costChangeEventTenant 额度change事件
     */
    @EventBusListener
    public void receiveDrugStorePackageCostChange(TenantPackageChangeEvent costChangeEventTenant) {
        try {
            tenantPackageCostService.changeTenantPackageCost(TenantPackageCostConvert.INSTANCE.convertCostDto(costChangeEventTenant.getMsg()));
        } catch (Exception e) {
            log.error(e.getMessage());
        }
    }

}
