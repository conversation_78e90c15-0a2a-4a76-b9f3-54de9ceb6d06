package com.xyy.saas.inquiry.drugstore.server.dal.dataobject.tenant;

import cn.iocoder.yudao.framework.tenant.core.db.TenantBaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.xyy.saas.inquiry.annotation.JsonTypeHandler;
import com.xyy.saas.inquiry.pojo.param.ParamConfigExtDto;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 门店参数配置 DO
 *
 * <AUTHOR>
 */
@TableName(value = "system_tenant_param_config", autoResultMap = true)
@KeySequence("system_tenant_param_config_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TenantParamConfigDO extends TenantBaseDO {

    /**
     * 主键
     */
    @TableId
    private Long id;
//    /**
//     * 门店id
//     */
//    private Long tenantId;

    /**
     * 系统类型 0-问诊,1-saas...
     */
    private Integer bizType;
    /**
     * 参数类型 eg:1荷叶问诊服务 x问诊小程序二维码
     */
    private Integer paramType;
    /**
     * 参数类型 eg:1荷叶问诊服务 x问诊小程序二维码
     */
    private String paramName;
    /**
     * 值 eg:1 开启 x http://xxx.jpg
     */
    private String paramValue;


    @TableField(typeHandler = JsonTypeHandler.class)
    private ParamConfigExtDto ext;

    @JsonIgnore
    public ParamConfigExtDto extGet() {
        if (ext == null) {
            ext = new ParamConfigExtDto();
        }
        return ext;
    }

    /**
     * 参数描述
     */
    private String description;

}