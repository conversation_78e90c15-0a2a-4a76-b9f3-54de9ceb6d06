package com.xyy.saas.inquiry.drugstore.server.mq.consumer.tenant;

import cn.iocoder.yudao.framework.tenant.core.util.TenantUtils;
import com.xyy.saas.eventbus.rocketmq.annotation.EventBusConsumer;
import com.xyy.saas.eventbus.rocketmq.annotation.EventBusListener;
import com.xyy.saas.inquiry.drugstore.server.controller.admin.drugstore.vo.TenantParamConfigSaveReqVO;
import com.xyy.saas.inquiry.drugstore.server.convert.tennat.TenantParamConfigConvert;
import com.xyy.saas.inquiry.drugstore.server.service.tenant.TenantParamConfigService;
import com.xyy.saas.inquiry.enums.tenant.TenantParamConfigTypeEnum;
import com.xyy.saas.inquiry.mq.tenant.TenantParamConfigDto;
import com.xyy.saas.inquiry.mq.tenant.TenantParamConfigEvent;
import jakarta.annotation.Resource;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/**
 * @Desc
 * <AUTHOR>  <EMAIL>
 * @Date Created in 2024/9/5 下午2:34 {@link cn.iocoder.yudao.module.system.service.tenant.TenantServiceImpl#saveTenantParamConfig(List)}
 */
@Component
@Slf4j
@EventBusConsumer(groupId = "com_xyy_saas_inquiry_drugstore_server_mq_consumer_param_DrugStoreParamConfigConsumer",
    topic = TenantParamConfigEvent.TOPIC)
public class DrugStoreParamConfigConsumer {

    public static final String GROUP_ID = DrugStoreParamConfigConsumer.class.getName();

    @Resource
    private TenantParamConfigService tenantParamConfigService;

    @EventBusListener
    public void receiveDrugStoreParamConfig(TenantParamConfigEvent inquiryRecordCreateEvent) {
        log.info("门店接收问诊参数配置receiveDrugStoreParamConfig,消息:{}", inquiryRecordCreateEvent.getMsg());
        for (TenantParamConfigDto configDto : inquiryRecordCreateEvent.getMsg()) {
            if (StringUtils.isBlank(configDto.getParamValue())) {
                continue;
            }
            try {
                TenantUtils.execute(configDto.getTenantId(), () -> {
                    TenantParamConfigSaveReqVO configSaveReqVO = TenantParamConfigConvert.INSTANCE.convert(TenantParamConfigTypeEnum.fromType(configDto.getParamType()), configDto.getParamValue());
                    tenantParamConfigService.saveOrUpdateTenantParamConfig(configSaveReqVO);
                });
            } catch (Exception e) {
                log.error(e.getMessage());
            }

        }
    }

}
