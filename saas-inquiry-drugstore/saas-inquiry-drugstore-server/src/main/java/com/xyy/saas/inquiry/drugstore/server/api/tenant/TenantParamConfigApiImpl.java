package com.xyy.saas.inquiry.drugstore.server.api.tenant;

import com.xyy.saas.inquiry.drugstore.api.tenant.TenantParamConfigApi;
import com.xyy.saas.inquiry.drugstore.api.tenant.dto.TenantParamConfigDTO;
import com.xyy.saas.inquiry.drugstore.server.convert.tennat.TenantParamConfigConvert;
import com.xyy.saas.inquiry.drugstore.server.dal.dataobject.tenant.TenantParamConfigDO;
import com.xyy.saas.inquiry.drugstore.server.service.tenant.TenantParamConfigService;
import com.xyy.saas.inquiry.enums.tenant.TenantParamConfigTypeEnum;
import jakarta.annotation.Resource;
import org.apache.dubbo.config.annotation.DubboService;
import java.util.List;

/**
 * 门店套餐包额度apiImpl
 *
 * @Author:chenxiaoyi
 * @Date:2024/09/05 13:54
 */
@DubboService
public class TenantParamConfigApiImpl implements TenantParamConfigApi {

    @Resource
    private TenantParamConfigService tenantParamConfigService;


    @Override
    public List<TenantParamConfigDTO> batchQueryTenantParamConfig(List<Long> tenantIds, TenantParamConfigTypeEnum type) {
        List<TenantParamConfigDO> paramConfigs = tenantParamConfigService.getTenantParamConfig(tenantIds, type);
        return TenantParamConfigConvert.INSTANCE.convertDtos(paramConfigs);
    }


    @Override
    public TenantParamConfigDTO queryTenantParamConfig(Long tenantId, TenantParamConfigTypeEnum type) {
        TenantParamConfigDO tenantParamConfig = tenantParamConfigService.getTenantParamConfig(tenantId, type);
        return TenantParamConfigConvert.INSTANCE.convertDto(tenantParamConfig);
    }

    @Override
    public String getParamConfigValueSelf2Sys(TenantParamConfigTypeEnum tenantParamConfigTypeEnum) {
        return tenantParamConfigService.getParamConfigValueSelf2Sys(tenantParamConfigTypeEnum);
    }

    @Override
    public Integer getParamConfigValueSelf2SysInteger(TenantParamConfigTypeEnum tenantParamConfigTypeEnum) {
        return tenantParamConfigService.getParamConfigValueSelf2SysInteger(tenantParamConfigTypeEnum);
    }

    @Override
    public String getParamConfigValueSys2Self(TenantParamConfigTypeEnum tenantParamConfigTypeEnum) {
        return tenantParamConfigService.getParamConfigValueSys2Self(tenantParamConfigTypeEnum);
    }

    @Override
    public Integer getParamConfigValueSys2SelfInteger(TenantParamConfigTypeEnum tenantParamConfigTypeEnum) {
        return tenantParamConfigService.getParamConfigValueSys2SelfInteger(tenantParamConfigTypeEnum);
    }
}
