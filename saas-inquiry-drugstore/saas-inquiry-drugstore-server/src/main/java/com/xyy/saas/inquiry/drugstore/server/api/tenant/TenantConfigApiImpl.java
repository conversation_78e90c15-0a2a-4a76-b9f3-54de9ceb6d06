package com.xyy.saas.inquiry.drugstore.server.api.tenant;

import com.xyy.saas.inquiry.drugstore.api.tenant.TenantParamConfigApi;
import com.xyy.saas.inquiry.trace.service.TenantConfigApi;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

/**
 * @Author:chenxia<PERSON>i
 * @Date:2025/06/27 14:40
 */
@Service
public class TenantConfigApiImpl implements TenantConfigApi {


    @Resource
    private TenantParamConfigApi tenantParamConfigApi;

    @Override
    public Integer getTenantPresDateType(Long tenantId) {
        return tenantParamConfigApi.getTenantPresDateType(tenantId);
    }
}
