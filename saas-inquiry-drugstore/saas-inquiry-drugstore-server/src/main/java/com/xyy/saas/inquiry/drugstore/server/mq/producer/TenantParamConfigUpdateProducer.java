package com.xyy.saas.inquiry.drugstore.server.mq.producer;

import com.xyy.saas.eventbus.rocketmq.annotation.EventBusProducer;
import com.xyy.saas.eventbus.rocketmq.core.original.EventBusRocketMQTemplate;
import com.xyy.saas.inquiry.drugstore.mq.message.TenantParamConfigUpdateEvent;
import org.springframework.stereotype.Component;

/**
 * @Desc 门店参数配置更新 producer
 * <AUTHOR>
 */
@Component
@EventBusProducer(
    topic = TenantParamConfigUpdateEvent.TOPIC
)
public class TenantParamConfigUpdateProducer extends EventBusRocketMQTemplate {


}
