package com.xyy.saas.inquiry.drugstore.server.api.option;

import com.xyy.saas.inquiry.drugstore.api.option.InquiryOptionConfigApi;
import com.xyy.saas.inquiry.drugstore.api.option.dto.InquiryOptionConfigQueryDto;
import com.xyy.saas.inquiry.drugstore.api.option.dto.InquiryOptionConfigRespDto;
import com.xyy.saas.inquiry.drugstore.api.option.dto.InquiryOptionGlobalConfigRespDto;
import com.xyy.saas.inquiry.drugstore.enums.InquiryOptionTypeEnum;
import com.xyy.saas.inquiry.drugstore.server.dal.dataobject.option.InquiryOptionConfigDO;
import com.xyy.saas.inquiry.drugstore.server.service.option.InquiryOptionConfigService;
import com.xyy.saas.inquiry.pojo.TenantDto;
import lombok.AllArgsConstructor;
import org.apache.dubbo.config.annotation.DubboService;

/**
 * desc
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
@DubboService
@AllArgsConstructor
public class InquiryOptionConfigApiImpl implements InquiryOptionConfigApi {

    private final InquiryOptionConfigService inquiryOptionConfigService;

    @Override
    public InquiryOptionGlobalConfigRespDto getInquiryOptionGlobalConfig(InquiryOptionConfigQueryDto queryDto) {
        return inquiryOptionConfigService.getInquiryOptionGlobalConfig(queryDto);
    }

    @Override
    public InquiryOptionConfigRespDto getInquiryOptionConfig(TenantDto tenantDto, InquiryOptionTypeEnum... optionTypeEnums) {
        return inquiryOptionConfigService.getInquiryOptionConfig(tenantDto, optionTypeEnums);
    }

    @Override
    public String getInquiryOptionValue(Long targetId, InquiryOptionTypeEnum optionTypeEnum) {
        InquiryOptionConfigDO inquiryOptionConfig = inquiryOptionConfigService.getInquiryOptionConfig(targetId, optionTypeEnum);
        return inquiryOptionConfig == null ? null : inquiryOptionConfig.getOptionValue();
    }
}
