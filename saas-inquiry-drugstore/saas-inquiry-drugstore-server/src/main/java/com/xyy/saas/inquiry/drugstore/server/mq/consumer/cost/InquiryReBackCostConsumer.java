package com.xyy.saas.inquiry.drugstore.server.mq.consumer.cost;

import com.xyy.saas.eventbus.rocketmq.annotation.EventBusConsumer;
import com.xyy.saas.eventbus.rocketmq.annotation.EventBusListener;
import com.xyy.saas.inquiry.mq.tenant.cost.InquiryReBackCostEvent;
import com.xyy.saas.inquiry.drugstore.server.service.tenant.TenantPackageCostService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Component
@Slf4j
@EventBusConsumer(groupId = "com_xyy_saas_inquiry_drugstore_server_mq_consumer_cost_InquiryReBackCostConsumer",
    topic = InquiryReBackCostEvent.TOPIC)
public class InquiryReBackCostConsumer {

    @Resource
    private TenantPackageCostService tenantPackageCostService;

    /**
     * 问诊重置返回额度Consumer
     *
     * @param inquiryReBackCostEvent 额度事件
     */
    @EventBusListener
    public void inquiryReBackCostConsumer(InquiryReBackCostEvent inquiryReBackCostEvent) {
        tenantPackageCostService.reBackTenantCost(inquiryReBackCostEvent.getMsg());
    }


}
