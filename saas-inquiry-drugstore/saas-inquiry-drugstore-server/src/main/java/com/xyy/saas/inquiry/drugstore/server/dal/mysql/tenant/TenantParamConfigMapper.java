package com.xyy.saas.inquiry.drugstore.server.dal.mysql.tenant;

import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.tenant.core.db.TenantBaseDO;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.xyy.saas.inquiry.drugstore.server.controller.admin.drugstore.vo.TenantParamConfigSaveReqVO;
import com.xyy.saas.inquiry.drugstore.server.dal.dataobject.tenant.TenantParamConfigDO;
import com.xyy.saas.inquiry.enums.tenant.TenantParamConfigTypeEnum;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Set;

/**
 * 门店参数配置 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface TenantParamConfigMapper extends BaseMapperX<TenantParamConfigDO> {


    default TenantParamConfigDO queryOneByCondition(TenantParamConfigSaveReqVO saveReqVO) {
        return selectOne(new LambdaQueryWrapperX<TenantParamConfigDO>()
            .eq(TenantParamConfigDO::getTenantId, saveReqVO.getTenantId())
            .eq(TenantParamConfigDO::getBizType, saveReqVO.getBizType())
            .eqIfPresent(TenantParamConfigDO::getParamType, saveReqVO.getParamType()), false);
    }

    default List<TenantParamConfigDO> selectByTenantId(Long tenantId) {
        return selectList(new LambdaQueryWrapperX<TenantParamConfigDO>().eqIfPresent(TenantParamConfigDO::getTenantId, tenantId));
    }


    default TenantParamConfigDO selectByType(Long tenantId, int type) {
        return selectOne(TenantParamConfigDO::getParamType, type, TenantParamConfigDO::getTenantId, tenantId);
    }

    default List<TenantParamConfigDO> selectByTypes(List<Long> tenantIds, Set<Integer> types) {
        return selectList(new LambdaQueryWrapperX<TenantParamConfigDO>().inIfPresent(TenantParamConfigDO::getTenantId, tenantIds).inIfPresent(TenantParamConfigDO::getParamType, types));
    }


    default void updateByParamType(TenantParamConfigDO updateObj) {
        update(new LambdaUpdateWrapper<TenantParamConfigDO>().set(TenantParamConfigDO::getParamValue, updateObj.getParamValue())
            .eq(TenantParamConfigDO::getBizType, updateObj.getBizType()).eq(TenantParamConfigDO::getParamType, updateObj.getParamType()));
    }

    default List<TenantParamConfigDO> selectByTenantIds(List<Long> tenantIds, TenantParamConfigTypeEnum tenantParamConfigTypeEnum) {
        return selectList(new LambdaQueryWrapperX<TenantParamConfigDO>().in(TenantBaseDO::getTenantId, tenantIds).eq(TenantParamConfigDO::getParamType, tenantParamConfigTypeEnum.getType()));
    }
}