package com.xyy.saas.inquiry.drugstore.server.controller.admin.drugstore.vo;

import com.xyy.saas.inquiry.enums.tenant.TenantParamConfigTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 对应field 方便前端控制回显参数 {@link TenantParamConfigTypeEnum}
 */
@Schema(description = "管理后台 - 门店参数配置 Response VO")
@Data
public class DrugStoreParamConfigRespVO {


    @Schema(description = "荷叶问诊服务", requiredMode = Schema.RequiredMode.REQUIRED, example = "0")
    private Integer inquiryServer;

    @Schema(description = "问诊审方类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Integer inquiryPresAuditType;

    @Schema(description = "问诊是否带出上次西药", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Integer inquiryWesternMedicineBring;

    @Schema(description = "问诊是否带出上次中药", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Integer inquiryChineseMedicineBring;

    @Schema(description = "处方日期格式", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Integer inquiryPresDateType;

    @Schema(description = "是否有平台审方生效套餐", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private boolean platformReview;

    @Schema(description = "小程序问诊需审核", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Integer inquiryPresAuditSwitch;
    @Schema(description = "远程审方流向总部药师", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Integer prescriptionRemoteToHeadSwitch;

    @Schema(description = "门店药师超时未审核处方时间", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Integer prescriptionRemoteToHeadTime;

}