package com.xyy.saas.inquiry.signature.dto.pdf;

import com.xyy.saas.inquiry.enums.prescription.template.TemplateFieldTypeEnum;
import lombok.Data;

import java.io.Serializable;

/**
 * desc
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
@Data
public class RectangleInfo implements Serializable {

    private String fieldName;
    /**
     * {@link TemplateFieldTypeEnum}
     */
    private int fieldType;
    private float x;
    private float y;
    private float width;
    private float height;
}
