package com.xyy.saas.inquiry.signature.api.signature.dto;

import com.xyy.saas.inquiry.pojo.BaseDto;
import com.xyy.saas.inquiry.signature.api.prescription.dto.PrescriptionParamDto;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * 签章合同 DO
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class InquiryFlushContractReqDto extends BaseDto {

    /**
     * 合同编号,系统生成
     */
    private String pref;

    /**
     * 门店id
     */
    private Long tenantId;

    // /**
    //  * 参与方集合
    //  */
    // private List<ParticipantItem> participants;

    /**
     * 合同参数
     */
    private PrescriptionParamDto paramDto;

}