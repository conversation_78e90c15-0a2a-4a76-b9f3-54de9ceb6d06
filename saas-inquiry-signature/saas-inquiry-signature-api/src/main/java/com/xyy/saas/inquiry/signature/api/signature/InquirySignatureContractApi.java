package com.xyy.saas.inquiry.signature.api.signature;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import com.xyy.saas.inquiry.enums.signature.ContractTypeEnum;
import com.xyy.saas.inquiry.signature.api.signature.dto.InquiryFlushContractReqDto;
import com.xyy.saas.inquiry.signature.api.signature.dto.InquiryFlushContractRespDto;
import com.xyy.saas.inquiry.signature.api.signature.dto.InquirySignatureContractDto;
import com.xyy.saas.inquiry.signature.dto.signature.ElectSignInfoDto;
import java.util.Map;

/**
 * 签章合同 Api 接口
 *
 * <AUTHOR>
 */
public interface InquirySignatureContractApi {

    /**
     * 获取签章平台合同 电子签信息
     *
     * @param bizId        业务id
     * @param contractType 合同类型
     * @return
     */
    Map<String, ElectSignInfoDto> getSignaturePlatformContractElectSignInfo(String bizId, ContractTypeEnum contractType);

    /**
     * 获取签章平台合同
     *
     * @param bizId
     * @param contractType
     * @return
     */
    InquirySignatureContractDto getSignaturePlatformContract(String bizId, ContractTypeEnum contractType);


    /**
     * 刷新且更新合同、重新生成pdf + 图片
     *
     * @return
     */
    CommonResult<InquiryFlushContractRespDto> flushContract(InquiryFlushContractReqDto reqDto);


}