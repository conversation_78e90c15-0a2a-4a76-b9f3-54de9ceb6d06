package com.xyy.saas.inquiry.signature.api.signature;

import com.xyy.saas.inquiry.enums.signature.SignatureBizTypeEnum;
import com.xyy.saas.inquiry.enums.signature.SignaturePlatformEnum;
import com.xyy.saas.inquiry.enums.system.RoleCodeEnum;

/**
 * 用户签章信息Api
 *
 * @Author:chenxiaoyi
 * @Date:2024/12/24 13:54
 */
public interface InquiryUserSignatureInformationApi {

    /**
     * 创建迁移用户签章信息
     *
     * @param nickName
     * @param signatureImgUrl
     * @param clockInStatus
     */
    void createMigrationUserSignatureInformation(String nickName, String signatureImgUrl, RoleCodeEnum roleCodeEnum, Integer clockInStatus);


    /**
     * 获取用户签名图片,先获取签章平台,如果没有获取Self getInquiryUserSignatureUrl
     *
     * @param userId                userId
     * @param signaturePlatformEnum 平台
     * @return 签章图片url
     */
    String getInquiryUserSignatureUrl(Long userId, SignaturePlatformEnum signaturePlatformEnum);

    /**
     * 获取用户签章信息
     *
     * @param userId                userId
     * @param signaturePlatformEnum 平台
     * @param signatureBizTypeEnum  业务类型
     * @return 签章图片url
     */
    String getInquiryUserSignatureUrl(Long userId, SignaturePlatformEnum signaturePlatformEnum, SignatureBizTypeEnum signatureBizTypeEnum);

}
