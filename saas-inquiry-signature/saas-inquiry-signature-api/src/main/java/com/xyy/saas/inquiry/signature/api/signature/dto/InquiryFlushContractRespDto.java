package com.xyy.saas.inquiry.signature.api.signature.dto;

import com.xyy.saas.inquiry.pojo.BaseDto;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * 签章合同 DO
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class InquiryFlushContractRespDto extends BaseDto {

    /**
     * 合同图片
     */
    private String imgUrl;
    /**
     * 合同PDF文件
     */
    private String pdfUrl;

}