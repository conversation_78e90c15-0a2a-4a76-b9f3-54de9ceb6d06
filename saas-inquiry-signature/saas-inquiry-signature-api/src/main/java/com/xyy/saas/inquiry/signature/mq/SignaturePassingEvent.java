package com.xyy.saas.inquiry.signature.mq;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.xyy.saas.eventbus.rocketmq.core.EventBusAbstractMessage;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 签章通过 - 消息传递事件
 *
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Builder
@Data
public class SignaturePassingEvent extends EventBusAbstractMessage {

    public static final String TOPIC = "SIGNATURE_PASSING";

    private SignaturePassingMessage msg;

    @JsonCreator
    public SignaturePassingEvent(@JsonProperty("msg") SignaturePassingMessage msg) {
        this.msg = msg;
    }


    @Override
    public String getTag() {
        return "";
    }

}
