package com.xyy.saas.inquiry.signature.dto.signature;

import com.xyy.saas.inquiry.enums.prescription.template.PrescriptionTemplateFieldEnum;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 电子签信息
 *
 * @Author:chenxiaoyi
 * @Date:2025/02/19 13:16
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class ElectSignInfoDto implements Serializable {

    /**
     * 参与方标识 对应  com.xyy.saas.inquiry.pojo.prescription.PrescriptionTemplateField.field 作为 合同 参与方标识 (用于定位当前级 和 查找下一级) {@link PrescriptionTemplateFieldEnum}
     */
    private String actorField;

    /**
     * 数字证书 X509格式规范的 BASE64编码数字证书值  (解析法大大合同pdf内容获取)
     */
    private String cerFile;

    /**
     * 签名值 - PKCS#1 格式 (解析法大大合同pdf内容获取)
     */
    private String signValue;

    /**
     * 数字签名时间戳 (解析法大大合同pdf内容获取)
     */
    private String signTimestamp;
}
