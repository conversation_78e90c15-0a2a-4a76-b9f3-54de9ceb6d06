package com.xyy.saas.inquiry.signature.enums;

/**
 * 签章同步平台状态
 *
 * @Author:<PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date:2025/02/20 10:51
 */
public enum SignatureSyncPlatformStatusEnum {

    INIT(0, "初始"),
    SYNCING(1, "同步中"),
    SYNCED(2, "已同步"),
    SYNC_FAIL(3, "同步失败");

    private int code;
    private String desc;

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    SignatureSyncPlatformStatusEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
