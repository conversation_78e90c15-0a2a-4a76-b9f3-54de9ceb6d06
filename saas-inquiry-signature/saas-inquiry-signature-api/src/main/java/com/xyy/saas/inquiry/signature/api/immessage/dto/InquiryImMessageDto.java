package com.xyy.saas.inquiry.signature.api.immessage.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @Author: xucao
 * @Date: 2024/12/03 16:16
 * @Description: Im 消息传输对象
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class InquiryImMessageDto implements Serializable {

    /**
     * 发送者姓名
     */
    private String userName;

    /**
     * 消息内容
     */
    private String msgContent;

    /**
     * 消息时间
     */
    private String msgTime;

    /**
     * 消息类型
     */
    private String sourceType;

}
