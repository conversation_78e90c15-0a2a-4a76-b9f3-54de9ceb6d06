package com.xyy.saas.inquiry.signature.api.prescriptiontemplate.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * @ClassName：InquiryPrescriptionTemplateDto
 * @Author: xucao
 * @Date: 2024/10/31 16:05
 * @Description: 处方笺模版板DTO
 */
@Data
public class InquiryPrescriptionTemplateDto implements Serializable {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "12132")
    private Long id;

    @Schema(description = "所属医院编码", requiredMode = Schema.RequiredMode.REQUIRED, example = "25982")
    private String hospitalPref;

    @Schema(description = "所属医院", requiredMode = Schema.RequiredMode.REQUIRED, example = "25982")
    private String hospitalName;

    @Schema(description = "处方笺模板名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "张三")
    private String name;

    @Schema(description = "处方笺模板类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    private Integer type;

    @Schema(description = "处方笺模板描述", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    private String desc;

    @Schema(description = "是否禁用", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    private Boolean disable;

    @Schema(description = "文件 URL（底版）", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    private String url0;

    @Schema(description = "文件 URL", requiredMode = Schema.RequiredMode.REQUIRED, example = "https://www.iocoder.cn")
    private String url;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDateTime createTime;
}
