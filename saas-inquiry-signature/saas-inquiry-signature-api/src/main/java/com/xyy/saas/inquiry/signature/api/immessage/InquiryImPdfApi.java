package com.xyy.saas.inquiry.signature.api.immessage;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import com.xyy.saas.inquiry.signature.api.immessage.dto.InquiryImMessageDto;

import java.util.List;

/**
 * @Author: xucao
 * @Date: 2024/12/03 16:15
 * @Description: Im 消息接口
 */
public interface InquiryImPdfApi {

    /**
     * 生成聊天pdf
     * @param dtoList 聊天消息列表
     * @return pdf url
     */
    CommonResult<String> genarateImPdf(List<InquiryImMessageDto> dtoList);


    /**
     * 生成聊天pdf
     * @param dtoList 聊天消息列表
     * @return pdf url
     */
    CommonResult<String> generateImPdfByFreeMarker(List<InquiryImMessageDto> dtoList);
}
