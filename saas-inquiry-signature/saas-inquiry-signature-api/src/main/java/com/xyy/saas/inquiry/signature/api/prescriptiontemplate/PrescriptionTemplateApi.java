package com.xyy.saas.inquiry.signature.api.prescriptiontemplate;

import com.xyy.saas.inquiry.signature.api.prescriptiontemplate.dto.InquiryPrescriptionTemplateDto;

import java.util.List;
import java.util.Map;

/**
 * @ClassName：PrescriptionTemplateApi
 * @Author: xucao
 * @Date: 2024/10/31 16:02
 * @Description: 处方笺模板api
 */
public interface PrescriptionTemplateApi {

    /**
     * 根据id获取模板信息
     *
     * @param id
     * @return
     */
    InquiryPrescriptionTemplateDto getInquiryPrescriptionTemplate(Long id);

    /**
     * 根据id获取模板信息
     *
     * @param ids
     * @return
     */
    Map<Long, InquiryPrescriptionTemplateDto> getInquiryPrescriptionTemplate(List<Long> ids);
}
