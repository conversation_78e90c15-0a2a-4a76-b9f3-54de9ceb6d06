package com.xyy.saas.inquiry.signature.api.prescription.dto;

import com.xyy.saas.inquiry.enums.doctor.AuditorTypeEnum;
import com.xyy.saas.inquiry.pojo.prescription.ParticipantItem;
import jakarta.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 处方签章Dto
 *
 * @Author:chenxiaoyi
 * @Date:2024/11/26 17:55
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class PrescriptionSignatureBaseDto implements Serializable {

    /**
     * 处方编码
     */
    @NotNull(message = "处方编码不能为空")
    private String prescriptionPref;

    /**
     * 问诊业务类型 1、药店问诊  5远程审方
     */
    private Integer inquiryBizType;

    /**
     * 处方笺图片url - 带方审方时候必填
     */
    private String prescriptionImgUrl;

    /**
     * 处方笺模板id
     */
    private Long templateId;

    /**
     * 处方合同编号
     */
    private String contractPref;

    /**
     * 需要转换的文档参数Map
     */
    private Map<String, String> paramMap;

    /**
     * 发起方
     */
    @NotNull(message = "发起方不能为空")
    private ParticipantItem participantItem;

    /**
     * 自绘合同标识  0-是,1-否 {@link cn.iocoder.yudao.framework.common.enums.CommonStatusEnum}
     */
    private Integer selfDrawn;

    /**
     * 审核人类型
     */
    @NotNull(message = "当前处理人类型不能为空")
    private AuditorTypeEnum auditorTypeEnum;
}
