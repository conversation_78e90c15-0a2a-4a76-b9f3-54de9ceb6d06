package com.xyy.saas.inquiry.signature.api.prescription.dto;

import com.xyy.saas.inquiry.enums.prescription.template.PrescriptionTemplateFieldEnum;
import com.xyy.saas.inquiry.pojo.inquiry.InquiryProductDto;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 处方参数Dto {@link PrescriptionTemplateFieldEnum}
 *
 * @Author:chenxiaoyi
 * @Date:2024/11/26 17:55
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class PrescriptionParamDto implements Serializable {


    /**
     * 门店id
     */
    private Long tenantId;

    /**
     * 处方编码
     */
    private String no;
    /**
     * 处方日期 ,按需转换日期格式:yyyy-MM-dd等
     */
    private String date;
    /**
     * 患者名称
     */
    private String name;
    /**
     * 患者性别
     */
    private String sex;
    /**
     * 患者年龄
     */
    private String age;
    /**
     * 患者身份证号
     */
    private String idCard;
    /**
     * 患者手机号
     */
    private String mobile;
    /**
     * 科室
     */
    private String dept;
    /**
     * 诊断
     */
    private String diagnosis;
    /**
     * 过敏史
     */
    private String allergic;
    /**
     * 说明
     */
    private String instruction;
    /**
     * 备注
     */
    private String remarks;
    /**
     * 警告
     */
    private String warning;

    /**
     * 医生医保编码
     */
    private String doctorMedicareNo;

    /**
     * 门诊号
     */
    private String iptOtpNo;

    /**
     * 医生电子签名图片
     */
    private String doctorElectronicPicture;

    /**
     * 药师电子签名图片
     */
    private String pharmacistElectronicPicture;

    /**
     * 处方划价金额
     */
    private String price;

    /**
     * 西药药品信息
     * <p>
     * 通用名 (规格) 【数量 包装单位】 * <p> 用法用量 : x ,一次 剂量 片 ,频次 \n
     * <p>
     * eg： 辅酶XX片（10mg*10s*3 板 糖衣）【3 盒】;
     * <p>
     * 用法用量：口服，一次 1 片，3 次/天1; \n
     * <p>
     * -------------以下空白----------------
     * <p>
     * 中草药 通用名 数量 g  \t\t 通用名 数量 g  \n
     * <p>
     * eg: 决明子 10g      人参  90g 通草   100g
     */
    private String drugs;

    /**
     * 用药类型 {@link com.xyy.saas.inquiry.enums.inquiry.MedicineTypeEnum}
     */
    private Integer medicineType;

    /**
     * 慢病病情需要 0 否  1是
     */
    private Integer slowDisease;

    /**
     * 问诊药品信息
     */
    private InquiryProductDto inquiryProductDto;
}
