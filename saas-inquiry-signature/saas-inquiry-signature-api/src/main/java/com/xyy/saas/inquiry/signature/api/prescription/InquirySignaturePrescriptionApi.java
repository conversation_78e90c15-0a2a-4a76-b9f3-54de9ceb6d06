package com.xyy.saas.inquiry.signature.api.prescription;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import com.xyy.saas.inquiry.signature.api.prescription.dto.PrescriptionSignatureAuditDto;
import com.xyy.saas.inquiry.signature.api.prescription.dto.PrescriptionSignatureInitDto;

/**
 * @Author:chenxiaoyi
 * @Date:2024/11/26 17:51
 */
public interface InquirySignaturePrescriptionApi {


    /**
     * 开具(签发)处方合同(基于文档)
     *
     * @param prescriptionSignatureInitDto 处方签章dto
     */
    CommonResult<?> issuePrescription(PrescriptionSignatureInitDto prescriptionSignatureInitDto);


    /**
     * 审核处方笺
     *
     * @param prescriptionSignatureAuditDto 审核dto
     */
    CommonResult<?> auditPrescription(PrescriptionSignatureAuditDto prescriptionSignatureAuditDto);

}
