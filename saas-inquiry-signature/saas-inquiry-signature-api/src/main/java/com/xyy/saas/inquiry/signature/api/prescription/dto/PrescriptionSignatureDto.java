package com.xyy.saas.inquiry.signature.api.prescription.dto;

import com.xyy.saas.inquiry.enums.doctor.AuditorTypeEnum;
import com.xyy.saas.inquiry.pojo.prescription.Coordinate;
import com.xyy.saas.inquiry.pojo.prescription.ParticipantItem;
import jakarta.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 处方签章Dto
 *
 * @Author:chenxiaoyi
 * @Date:2024/11/26 17:55
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class PrescriptionSignatureDto implements Serializable {

    /**
     * 处方合同ID
     */
    private String contractPref;

    /**
     * 处方笺模板id
     */
    private Long templateId;

    /**
     * 审核记录表ID
     */
    private Long auditRecordId;

    /**
     * 需要转换的文档参数Map
     */
    private Map<String, String> paramMap;

    /**
     * 参与方列表
     */
    @NotNull(message = "参与方列表不能为空")
    private List<ParticipantItem> participantItems;

    /**
     * 自绘合同标识  0-是,1-否 {@link cn.iocoder.yudao.framework.common.enums.CommonStatusEnum}
     */
    private Integer selfDrawn;

    /**
     * 审核人类型
     */
    private AuditorTypeEnum auditorTypeEnum;

    /**
     * 问诊业务类型 1、药店问诊  5远程审方
     */
    private Integer inquiryBizType;

    /**
     * 处方笺图片url - 带方审方时候必填
     */
    private String prescriptionImgUrl;

    /**
     * 签名坐标,带方审方时不可为空
     */
    private Coordinate coordinate;

}
