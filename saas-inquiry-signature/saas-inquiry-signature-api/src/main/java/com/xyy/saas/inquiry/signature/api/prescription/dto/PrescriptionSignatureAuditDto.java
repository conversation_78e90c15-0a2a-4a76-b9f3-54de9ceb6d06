package com.xyy.saas.inquiry.signature.api.prescription.dto;

import com.xyy.saas.inquiry.pojo.prescription.Coordinate;
import com.xyy.saas.inquiry.pojo.prescription.ParticipantItem;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 处方签章Dto
 *
 * @Author:chenxia<PERSON>i
 * @Date:2024/11/26 17:55
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class PrescriptionSignatureAuditDto extends PrescriptionSignatureBaseDto {

    /**
     * 是否自动审方
     */
    private boolean autoAudit;

    /**
     * 审核记录表ID
     */
    private Long auditRecordId;


    /**
     * 参与方列表
     */
    private List<ParticipantItem> participantItems;

    /**
     * 签章平台标识
     */
    private Integer platform;

    /**
     * 签名坐标,带方审方时不可为空
     */
    private Coordinate coordinate;
}
