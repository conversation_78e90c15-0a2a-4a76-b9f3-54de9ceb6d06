package com.xyy.saas.inquiry.signature.api.signature.dto;

import com.xyy.saas.inquiry.pojo.BaseDto;
import com.xyy.saas.inquiry.pojo.prescription.ParticipantItem;
import com.xyy.saas.inquiry.pojo.signature.SignatureContractExtDto;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * 签章合同 DO
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class InquirySignatureContractDto extends BaseDto {

    /**
     * 主键
     */
    private Long id;
    /**
     * 合同编号,系统生成
     */
    private String pref;
    /**
     * 签章平台  0-无 1-法大大 {@link com.xyy.saas.inquiry.enums.signature.SignaturePlatformEnum}
     */
    private Integer signaturePlatform;

    /**
     * 合同状态
     */
    private Integer contractStatus;

    /**
     * 自绘合同标识  0-是,1-否 {@link cn.iocoder.yudao.framework.common.enums.CommonStatusEnum}
     */
    private Integer selfDrawn;

    /**
     * 合同业务类型 {@link com.xyy.saas.inquiry.enums.signature.ContractTypeEnum}
     */
    private Integer contractType;
    /**
     * 业务方唯一标识
     */
    private String bizId;
    /**
     * 三方签署任务id signTaskId
     */
    private String thirdId;

    /**
     * 模板id - 使用String兼容其他场景
     */
    private String templateId;

    /**
     * 发起方uerId
     */
    private Long initiatorUserId;
    /**
     * 发起方姓名
     */
    private String initiatorName;
    /**
     * 发起方联系方式
     */
    private String initiatorMobile;
    /**
     * 参与方集合
     */
    private List<ParticipantItem> participants;
    /**
     * 合同参数详情 json
     */
    private String paramDetail;
    /**
     * 合同图片
     */
    private String imgUrl;
    /**
     * 合同PDF文件
     */
    private String pdfUrl;


    /**
     * 同步平台
     */
    private Integer syncPlatform;

    /**
     * 同步平台状态
     */
    private Integer syncPlatformStatus;

    /**
     * ext
     */
    private SignatureContractExtDto ext;
}