package com.xyy.saas.inquiry.signature.dto.platform;

import lombok.Data;

import java.io.Serializable;

/**
 * 签章平台基础配置类
 *
 * @Author:chenxiaoyi
 * @Date:2024/02/27 10:21
 */
@Data
public class PlatformConfigDto implements Serializable {

    // 私有云地址
    private String privateUrl;

    private String appId;
    private String appSecret;
    // 免验证签场景码
    private String unSignBusinessId;
    // 东方中讯免签场景码
    private String unSignBusinessIdEzca;
    // 主应用OpenCorpId
    private String mainOpenCorpId;
    // 签章授权协议模板Id
    private String signAuthAgreementTempId;

}
