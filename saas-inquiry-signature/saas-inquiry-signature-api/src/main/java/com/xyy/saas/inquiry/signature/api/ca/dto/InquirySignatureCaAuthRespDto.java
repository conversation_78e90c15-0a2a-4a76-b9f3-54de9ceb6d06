package com.xyy.saas.inquiry.signature.api.ca.dto;

import com.xyy.saas.inquiry.pojo.signature.SignatureCAExtDto;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "管理后台 - CA认证 Response Dto")
@Data
@Accessors(chain = true)
public class InquirySignatureCaAuthRespDto implements Serializable {

    @Schema(description = "主键", example = "32662")
    private Long id;

    @Schema(description = "用户id", example = "16240")
    private Long userId;

    @Schema(description = "姓名", example = "16240")
    private String name;

    @Schema(description = "身份证号", example = "16240")
    private String idCard;

    @Schema(description = "签章平台 0-自签署 1-法大大")
    private Integer signaturePlatform;

    @Schema(description = "实名认证状态 0: 待认证，1: 认证完成，2: 认证失败", example = "2")
    private Integer certifyStatus;

    @Schema(description = "签名状态 0: 未签名，1: 已签名", example = "2")
    private Integer signatureStatus;

    @Schema(description = "免签授权状态 0: 未授权，1: 已授权，", example = "1")
    private Integer authorizeFreeSignStatus;

    @Schema(description = "免签授权截止时间")
    private LocalDateTime authorizeFreeSignDdl;

    @Schema(description = "免验证签署授权是否临期")
    private boolean authorizeFreeSignExpiring;

    @Schema(description = "授权协议签署状态 0: 未签署，1: 已签署", example = "1")
    private Integer authorizeAgreementStatus;

    @Schema(description = "兼职协议状态 0: 未签署，1: 已签署", example = "1")
    private Integer partTimeAgreementStatus;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    /**
     * CA认证扩展信息-其他应用下得CA认证信息
     */
    private List<SignatureCAExtDto> ext;


    @Schema(description = "认证是否完成(主应用)")
    private boolean caAuthCompleted;

    @Schema(description = "签名图片Url")
    private String signatureUrl;

    @Schema(description = "是否降级")
    private boolean drawnSign;

    @Schema(description = "是否降级完成")
    private boolean drawnSignCompleted;

    @Schema(description = "降级签名URL")
    private String drawnSignUrl;

}