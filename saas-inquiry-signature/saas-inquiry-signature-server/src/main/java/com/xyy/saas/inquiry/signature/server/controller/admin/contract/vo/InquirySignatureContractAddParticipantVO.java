package com.xyy.saas.inquiry.signature.server.controller.admin.contract.vo;

import com.xyy.saas.inquiry.pojo.prescription.ParticipantItem;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Builder;
import lombok.Data;

import java.util.List;

@Schema(description = "追加合同参与方")
@Data
@Builder
public class InquirySignatureContractAddParticipantVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "4478")
    private Long id;

    @Schema(description = "处方笺模板id", requiredMode = Schema.RequiredMode.REQUIRED, example = "4478")
    @NotNull(message = "处方笺模板id不能为空")
    private Long templateId;

    @Schema(description = "签章平台  1-法大大", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "签章平台  1-法大大不能为空")
    private Integer signaturePlatform;

    /**
     * {@link com.xyy.saas.inquiry.enums.signature.ContractTypeEnum}
     */
    @Schema(description = "合同业务类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    private Integer contractType;

    @Schema(description = "业务方唯一标识", requiredMode = Schema.RequiredMode.REQUIRED, example = "2369")
    @NotEmpty(message = "业务方唯一标识不能为空")
    private String bizId;

    @Schema(description = "参与方集合")
    private List<ParticipantItem> participants;

    @Schema(description = "当前参与方", requiredMode = Schema.RequiredMode.REQUIRED)
    private ParticipantItem participantItem;

    /**
     * 是否自动审方
     */
    private boolean autoAudit;

}