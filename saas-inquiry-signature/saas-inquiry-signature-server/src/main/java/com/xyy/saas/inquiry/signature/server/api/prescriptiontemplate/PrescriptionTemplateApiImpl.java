package com.xyy.saas.inquiry.signature.server.api.prescriptiontemplate;

import com.xyy.saas.inquiry.signature.api.prescriptiontemplate.PrescriptionTemplateApi;
import com.xyy.saas.inquiry.signature.api.prescriptiontemplate.dto.InquiryPrescriptionTemplateDto;
import com.xyy.saas.inquiry.signature.server.controller.admin.prescriptiontemplate.vo.InquiryPrescriptionTemplateRespVO;
import com.xyy.saas.inquiry.signature.server.convert.prescriptiontemplate.PrescriptionTemplateConvert;
import com.xyy.saas.inquiry.signature.server.service.prescriptiontemplate.InquiryPrescriptionTemplateService;
import jakarta.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import org.apache.dubbo.config.annotation.DubboService;

/**
 * @ClassName：PrescriptionTemplateApiImpl
 * @Author: xucao
 * @Date: 2024/10/31 16:09
 * @Description: 处方笺模板 API 实现类
 */
@DubboService
public class PrescriptionTemplateApiImpl implements PrescriptionTemplateApi {

    @Resource
    private InquiryPrescriptionTemplateService prescriptionTemplateService;

    /**
     * 根据id获取模板信息
     *
     * @param id
     * @return
     */
    @Override
    public InquiryPrescriptionTemplateDto getInquiryPrescriptionTemplate(Long id) {
        return PrescriptionTemplateConvert.INSTANCE.convertRespVO2DTO(prescriptionTemplateService.getInquiryPrescriptionTemplate(id));
    }

    /**
     * 根据id获取模板信息
     *
     * @param ids
     * @return
     */
    @Override
    public Map<Long, InquiryPrescriptionTemplateDto> getInquiryPrescriptionTemplate(List<Long> ids) {
        return prescriptionTemplateService.listInquiryPrescriptionTemplate(ids)
            .stream().collect(Collectors.toMap(InquiryPrescriptionTemplateRespVO::getId, PrescriptionTemplateConvert.INSTANCE::convertRespVO2DTO, (a, b) -> a));
    }
}
