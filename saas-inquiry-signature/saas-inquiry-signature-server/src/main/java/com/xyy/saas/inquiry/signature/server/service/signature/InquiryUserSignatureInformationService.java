package com.xyy.saas.inquiry.signature.server.service.signature;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import com.xyy.saas.inquiry.enums.signature.SignatureBizTypeEnum;
import com.xyy.saas.inquiry.enums.signature.SignaturePlatformEnum;
import com.xyy.saas.inquiry.enums.system.RoleCodeEnum;
import com.xyy.saas.inquiry.signature.server.controller.admin.signature.vo.InquiryUserElectronicSignatureSaveVO;
import com.xyy.saas.inquiry.signature.server.controller.app.signature.vo.InquirySignatureRoleVO;
import com.xyy.saas.inquiry.signature.server.controller.app.signature.vo.InquiryUserSignatureInformationVO;
import com.xyy.saas.inquiry.signature.server.controller.app.signature.vo.InquiryUserSignatureManageVO;
import com.xyy.saas.inquiry.signature.server.dal.dataobject.signature.InquiryUserSignatureInformationDO;
import jakarta.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * 问诊用户(医生/药师/核对/调配)签章信息 Service 接口
 *
 * <AUTHOR>
 */
public interface InquiryUserSignatureInformationService {

    /**
     * app 处方签名管理-创建签名
     *
     * @param createReqVO
     * @return 签名关系id
     */
    Long createInquiryUserSignature(InquiryUserSignatureManageVO createReqVO);

    /**
     * app 处方签名管理-修改签名
     *
     * @param updateReqVO 修改vo
     * @return 签名关系id
     */
    Long updateInquiryUserSignature(InquiryUserSignatureManageVO updateReqVO);


    /**
     * 存储用户签名信息
     *
     * @param createReqVO
     * @return
     */
    InquiryUserSignatureInformationDO saveOrUpdateInquiryUserSign(InquiryUserSignatureInformationVO informationVO);

    /**
     * 获取问诊用户签名详情
     *
     * @param userId                userId
     * @param signaturePlatformEnum 平台
     * @return 签名详情
     */
    InquiryUserSignatureManageVO getInquiryUserSignatureTenant(Long userId, SignaturePlatformEnum signaturePlatformEnum);

    /**
     * 获取用户签名图片,先获取签章平台,如果没有获取Self
     *
     * @param userId                userId
     * @param signaturePlatformEnum 平台
     * @return 签章图片url
     */
    String getInquiryUserSignature(Long userId, SignaturePlatformEnum signaturePlatformEnum, SignatureBizTypeEnum signatureBizTypeEnum);

    /**
     * 获取多个用户签名图片Map
     *
     * @param userIds               用户id列表
     * @param signaturePlatformEnum 平台
     * @return Map<userId, 签章图片url>
     */
    Map<Long, String> getInquiryUserSignatures(List<Long> userIds, SignaturePlatformEnum signaturePlatformEnum);


    /**
     * 根据签署任务id签署合同数据
     *
     * @param signTaskId            签署任务id
     * @param signaturePlatformEnum 平台
     */
    void deleteBySignTaskId(String signTaskId, SignaturePlatformEnum signaturePlatformEnum);


    /**
     * 根据条件查询一条用户签章信息
     *
     * @param informationVO info
     * @return vo
     */
    InquiryUserSignatureInformationVO queryOneByCondition(InquiryUserSignatureInformationVO informationVO);

    /**
     * 更新用户签章信息
     *
     * @param informationVO
     */
    void updateUserSignatureInformation(InquiryUserSignatureInformationVO informationVO);

    /**
     * 创建用户电子签章图片签名
     *
     * @param createReqVO
     * @return
     */
    CommonResult<String> createUserElectronicSignature(@Valid InquiryUserElectronicSignatureSaveVO createReqVO);

    /**
     * 删除用户电子签章图片签名
     *
     * @param updateVO
     * @return
     */
    CommonResult<?> deleteUserElectronicSignature(@Valid InquiryUserElectronicSignatureSaveVO updateVO);

    /**
     * 获取签名角色列表
     *
     * @return
     */
    List<InquirySignatureRoleVO> getSignatureRoleList();

    /**
     * 创建迁移用户签名信息
     *
     * @param nickName
     * @param signatureImgUrl
     * @param clockInStatus
     */
    void createMigrationUserSignatureInformation(String nickName, String signatureImgUrl, RoleCodeEnum roleCodeEnum, Integer clockInStatus);
}