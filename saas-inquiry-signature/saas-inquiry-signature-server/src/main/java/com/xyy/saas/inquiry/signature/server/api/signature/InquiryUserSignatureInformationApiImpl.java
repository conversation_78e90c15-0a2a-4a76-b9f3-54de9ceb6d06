package com.xyy.saas.inquiry.signature.server.api.signature;

import com.xyy.saas.inquiry.enums.signature.SignatureBizTypeEnum;
import com.xyy.saas.inquiry.enums.signature.SignaturePlatformEnum;
import com.xyy.saas.inquiry.enums.system.RoleCodeEnum;
import com.xyy.saas.inquiry.signature.api.signature.InquiryUserSignatureInformationApi;
import com.xyy.saas.inquiry.signature.server.service.signature.InquiryUserSignatureInformationService;
import jakarta.annotation.Resource;
import org.apache.dubbo.config.annotation.DubboService;

/**
 * 用户签章信息Api
 *
 * @Author:chenxiaoyi
 * @Date:2024/12/24 13:54
 */
@DubboService
public class InquiryUserSignatureInformationApiImpl implements InquiryUserSignatureInformationApi {

    @Resource
    private InquiryUserSignatureInformationService inquiryUserSignatureInformationService;

    @Override
    public void createMigrationUserSignatureInformation(String nickName, String signatureImgUrl, <PERSON><PERSON><PERSON><PERSON><PERSON> roleCodeEnum, Integer clockInStatus) {
        inquiryUserSignatureInformationService.createMigrationUserSignatureInformation(nickName, signatureImgUrl, roleCodeEnum, clockInStatus);
    }

    @Override
    public String getInquiryUserSignatureUrl(Long userId, SignaturePlatformEnum signaturePlatformEnum) {
        return inquiryUserSignatureInformationService.getInquiryUserSignature(userId, signaturePlatformEnum, SignatureBizTypeEnum.USER_HAND_DRAWN_SIGN);
    }

    @Override
    public String getInquiryUserSignatureUrl(Long userId, SignaturePlatformEnum signaturePlatformEnum, SignatureBizTypeEnum signatureBizTypeEnum) {
        return inquiryUserSignatureInformationService.getInquiryUserSignature(userId, signaturePlatformEnum, signatureBizTypeEnum);
    }
}
