package com.xyy.saas.transmitter.server.service.transmission;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.xyy.saas.transmitter.enums.ErrorCodeConstants.TENANT_TRANSMISSION_SERVICE_PACK_NOT_EXISTS;
import static com.xyy.saas.transmitter.enums.ErrorCodeConstants.TRANSMISSION_PROTOCOL_CONFIG_NOT_EXISTS;
import static com.xyy.saas.transmitter.enums.ErrorCodeConstants.TRANSMISSION_PROTOCOL_CONFIG_PARSE_FAILED;
import static com.xyy.saas.transmitter.enums.ErrorCodeConstants.TRANSMISSION_REQUEST_FAILED;
import static com.xyy.saas.transmitter.enums.ErrorCodeConstants.formatError;

import cn.hutool.core.lang.TypeReference;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.util.json.JsonUtils;
import cn.iocoder.yudao.framework.tenant.core.context.TenantContextHolder;
import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.databind.JsonNode;
import com.xyy.saas.inquiry.enums.transmitter.DslTypeEnum;
import com.xyy.saas.inquiry.enums.transmitter.RequestStatusEnum;
import com.xyy.saas.localserver.medicare.dsl.config.base.ContractConfig;
import com.xyy.saas.localserver.medicare.dsl.config.base.DSLConfig;
import com.xyy.saas.localserver.medicare.dsl.executor.DSLContext;
import com.xyy.saas.localserver.medicare.dsl.executor.DSLContextHolder;
import com.xyy.saas.localserver.medicare.dsl.executor.DSLExecutor;
import com.xyy.saas.localserver.medicare.dsl.executor.log.ContractInvokeContext;
import com.xyy.saas.localserver.medicare.dsl.executor.log.ContractInvokeContextHolder;
import com.xyy.saas.localserver.medicare.dsl.executor.value.DynamicBusiness;
import com.xyy.saas.localserver.medicare.dsl.reader.StringConfigReader;
import com.xyy.saas.transmitter.api.config.dto.TransmissionConfigItemDTO;
import com.xyy.saas.transmitter.api.config.dto.TransmissionConfigPackageDTO;
import com.xyy.saas.transmitter.api.servicepack.dto.TransmissionServicePackDTO;
import com.xyy.saas.transmitter.api.transmission.dto.TransmissionConfigReqDTO;
import com.xyy.saas.transmitter.api.transmission.dto.TransmissionReqDTO;
import com.xyy.saas.transmitter.api.transmission.dto.TransmissionRespDTO;
import com.xyy.saas.transmitter.api.transmission.dto.TransmissionRespDTO.TransmissionRespDTOBuilder;
import com.xyy.saas.transmitter.server.constant.TransmitterConstants;
import com.xyy.saas.transmitter.server.controller.admin.task.vo.TransmissionTaskRecordRespVO;
import com.xyy.saas.transmitter.server.controller.admin.task.vo.TransmissionTaskRecordSaveReqVO;
import com.xyy.saas.transmitter.server.convert.transmission.TransmissionConvert;
import com.xyy.saas.transmitter.server.service.servicepack.TransmissionServicePackService;
import com.xyy.saas.transmitter.server.service.task.TransmissionTaskRecordService;
import com.xyy.saas.transmitter.server.service.transmission.factory.LogicConfigProcessorFactory;
import com.xyy.saas.transmitter.server.service.transmission.processor.LogicConfigProcessor;
import com.xyy.saas.transmitter.server.service.transmission.processor.LogicConfigProcessor.ProcessContext;
import jakarta.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

/**
 * 数据传输服务实现类 - 领域服务 负责处理数据传输相关的业务逻辑
 * <p>
 * 核心职责： 1. 协议配置管理：验证和管理传输协议配置 2. 任务生命周期：处理任务的创建和执行 3. 服务包管理：维护服务包与任务的关系
 * <p>
 * 设计原则： 1. 单一职责：每个方法只负责一个特定功能 2. 分层处理：清晰的业务逻辑分层 3. 异常隔离：统一的异常处理机制
 *
 * <AUTHOR>
 */
@Service
@Slf4j
@Validated
public class TransmissionServiceImpl implements TransmissionService {

    // ===================== 1. 常量定义 =====================
    private static final String METHOD_CONTRACTS_INVOKE = "contractsInvoke";
    private static final String METHOD_CONTRACT_INVOKE = "contractInvoke";
    private static final String VALIDATE_BUSINESS_LOGIC = "validateBusinessLogic";
    private static final String CONTRACT_INVOKE_PARAM_NETWORK = "network";
    private static final String CONTRACT_INVOKE_PARAM_UPSTREAM_RESPONSE = "upstreamResponse";

    // ===================== 2. 依赖注入 =====================
    @Resource
    private TransmissionServicePackService servicePackService;
    @Resource
    private TransmissionTaskRecordService taskRecordService;
    @Resource
    private LogicConfigProcessorFactory logicConfigProcessorFactory;

    // ===================== 3. 公共接口方法 =====================

    /**
     * 验证协议配置是否有效 校验租户的传输协议配置是否可用
     * <p>
     * 校验流程： 1. 获取租户标识 2. 查询并验证服务包
     *
     * @param transmissionConfigReqDTO 传输配置请求对象
     * @return 配置是否有效
     */
    @Override
    public boolean validProtocolConfig(TransmissionConfigReqDTO transmissionConfigReqDTO) {
        return validateAndGetServicePacks(transmissionConfigReqDTO, true).isValid();
    }

    /**
     * 前置业务校验 校验待传输的数据是否符合要求(不强制校验协议配置是否存在)
     * <p>
     * 校验流程： 1. 获取租户标识 2. 查询并验证服务包 3. 执行业务校验
     *
     * @param transmissionReqDTO 传输请求对象
     * @return 校验结果
     */
    @Override
    public CommonResult<Boolean> validateBusinessLogic(TransmissionReqDTO transmissionReqDTO) {
        return executeWithErrorHandling(VALIDATE_BUSINESS_LOGIC, transmissionReqDTO, () -> {
            // 1. 验证服务包配置
            ValidationResult validationResult = validateAndGetServicePacks(transmissionReqDTO.getConfig(), false);
            if (!validationResult.isValid()) {
                return CommonResult.error(TENANT_TRANSMISSION_SERVICE_PACK_NOT_EXISTS);
            }

            // 2. 执行业务校验
            return validateBusinessLogic(transmissionReqDTO, validationResult.getServicePacks());
        });
    }

    /**
     * 执行数据传输(多服务包配置相同节点-药监的模式) 多协议调用，循环请求 处理数据传输任务，支持同步和异步模式
     * <p>
     * 处理流程： 1. 参数校验：验证请求参数的完整性 2. 任务创建：构建传输任务实体 3. 任务执行：处理数据传输逻辑 4. 结果处理：处理传输结果和异常
     * <p>
     * 特性支持： 1. 同步/异步：支持两种传输模式 2. 重试机制：失败任务自动重试 3. 下游处理：支持级联任务处理
     *
     * @param transmissionReqDTO 传输请求对象，包含传输数据和配置信息
     * @param clazz              响应数据类型的Class对象
     * @param <T>                响应数据类型
     * @return 传输响应列表，包含处理结果和响应数据
     */
    @Override
    public <T> List<TransmissionRespDTO<T>> contractsInvoke(TransmissionReqDTO transmissionReqDTO, Class<T> clazz) {
        return executeWithErrorHandling(METHOD_CONTRACTS_INVOKE, transmissionReqDTO, () -> {
            // 1. 验证服务包配置
            ValidationResult validationResult = validateAndGetServicePacks(transmissionReqDTO.getConfig(), true);
            if (!validationResult.isValid()) {
                return Collections
                    .singletonList(TransmissionRespDTO.<T>builder()
                        .result(CommonResult
                            .error(formatError(TENANT_TRANSMISSION_SERVICE_PACK_NOT_EXISTS, null)))
                        .build());
            }

            // 2. 执行任务处理
            return handleTransmission(transmissionReqDTO, validationResult.getServicePacks(), clazz);
        });
    }

    /**
     * 执行数据传输(节点唯一) 单一协议调用，如果匹配到多个服务包则取第一个服务包的节点调用 处理数据传输任务，支持同步和异步模式
     * <p>
     * 处理流程： 1. 参数校验：验证请求参数的完整性 2. 任务创建：构建传输任务实体 3. 任务执行：处理数据传输逻辑 4. 结果处理：处理传输结果和异常
     * <p>
     * 特性支持： 1. 同步/异步：支持两种传输模式 2. 重试机制：失败任务自动重试 3. 下游处理：支持级联任务处理
     *
     * @param transmissionReqDTO 传输请求对象，包含传输数据和配置信息
     * @param clazz              响应数据类型的Class对象
     * @param <T>                响应数据类型
     * @return 传输响应列表，包含处理结果和响应数据
     */
    @Override
    public <T> CommonResult<T> contractInvoke(TransmissionReqDTO transmissionReqDTO, Class<T> clazz) {
        return executeWithErrorHandling(METHOD_CONTRACT_INVOKE, transmissionReqDTO, () -> {
            // 1. 验证服务包配置
            ValidationResult validationResult = validateAndGetServicePacks(transmissionReqDTO.getConfig(), true);
            if (!validationResult.isValid()) {
                return CommonResult.error(TENANT_TRANSMISSION_SERVICE_PACK_NOT_EXISTS);
            }

            // 2. 验证是否存在多服务包同节点
            if (validationResult.getServicePacks().size() > 1) {
                log.warn("[contractInvoke][租户({})存在多服务包同节点] nodeType:{}",
                    transmissionReqDTO.getConfig().getTenantId(),
                    transmissionReqDTO.getConfig().getNodeType().getDesc());
            }

            // 3. 执行任务处理
            TransmissionRespDTO<T> respDTO = handleTransmission(transmissionReqDTO,
                Collections.singletonList(validationResult.getServicePacks().getFirst()),
                clazz).getFirst();
            transmissionReqDTO.setTaskId(respDTO.getTaskId());
            return respDTO.getResult();
        });
    }

    /**
     * 执行远程调用
     *
     * @param tenantId    租户ID
     * @param servicePack 服务包
     * @param params      业务参数
     * @param resultClass 返回值类型
     * @return 调用结果
     */
    @Override
    public <T> T executeRemoteCall(Long tenantId, TransmissionServicePackDTO servicePack, Map<String, Object> params,
        Class<T> resultClass) {

        // 1. 获取协议配置和公共配置
        String protocol = getProtocol(servicePack);
        String commonProtocol = getCommonProtocol(servicePack);

        // 2. 解析YAML配置，如果解析失败会抛出异常
        ContractConfig contractConfig = parseProtocolConfig(protocol, commonProtocol);
        DSLContext context = DSLContextHolder.getContext();

        // 3. 设置业务参数到上下文
        DynamicBusiness dynamicBusiness = TransmissionConvert.INSTANCE.buildDynamicBusiness(tenantId, servicePack, params);
        context.setBusiness(dynamicBusiness);

        // 4. 执行协议调用
        return DSLExecutor.instance.contractInvoker(contractConfig, resultClass);
    }

    // ===================== 4. 核心业务逻辑 =====================

    /**
     * 执行业务逻辑校验 遍历服务包执行具体的业务校验逻辑
     * <p>
     * 校验流程： 1. 遍历服务包：依次处理每个服务包 2. 配置校验：验证配置包的完整性 3. 业务校验：执行具体的业务规则校验 4. 结果汇总：合并所有校验结果
     * <p>
     * 异常处理： - 单个服务包校验失败不影响其他服务包 - 记录详细的校验失败日志 - 返回统一的校验结果
     *
     * @param transmissionReqDTO 传输请求对象
     * @param servicePacks       服务包列表
     * @return 校验结果，包含校验状态和错误信息
     */
    private CommonResult<Boolean> validateBusinessLogic(
        TransmissionReqDTO transmissionReqDTO,
        List<TransmissionServicePackDTO> servicePacks) {

        for (TransmissionServicePackDTO servicePack : servicePacks) {
            try {
                // 1. 校验配置包
                if (isInvalidConfigPackage(servicePack.getConfigPackage())) {
                    log.warn("[validateBusinessLogic][租户({})服务包({})配置为空] nodeType:{}",
                        transmissionReqDTO.getConfig().getTenantId(),
                        servicePack.getId(),
                        transmissionReqDTO.getConfig().getNodeType().getDesc());
                    continue;
                }

                // 2. 执行业务校验
                LogicConfigProcessor processor = logicConfigProcessorFactory.getProcessor(
                    transmissionReqDTO.getConfig().getNodeType());
                CommonResult<ProcessContext> preValidResult = processor.preProcess(transmissionReqDTO, servicePack);

                if (preValidResult.isSuccess() && Objects.nonNull(preValidResult.getData())) {
                    return CommonResult.success(true);
                }
            } catch (Exception e) {
                log.error("[validateBusinessLogic][租户({})服务包({})校验异常] nodeType:{}",
                    transmissionReqDTO.getConfig().getTenantId(),
                    servicePack.getId(),
                    transmissionReqDTO.getConfig().getNodeType().getDesc(),
                    e);
            }
        }

        return CommonResult.success(false);
    }

    /**
     * 验证配置包是否无效 检查配置包的完整性和有效性
     * <p>
     * 校验内容： 1. 非空校验：配置包对象不为空 2. 配置项校验：至少包含一个配置项 3. 状态校验：配置包处于可用状态
     *
     * @param configPackage 配置包对象
     * @return 配置包是否无效
     */
    private boolean isInvalidConfigPackage(TransmissionConfigPackageDTO configPackage) {
        return configPackage == null || CollectionUtils.isEmpty(configPackage.getConfigItems());
    }

    // ===================== 5. 服务包处理 =====================

    /**
     * 验证服务包配置 统一处理服务包的验证逻辑
     * <p>
     * 处理流程： 1. 设置租户信息 2. 查询并验证服务包 3. 验证服务包有效性
     *
     * @param transmissionConfigReqDTO   传输请求配置信息
     * @param needValidateProtocolConfig 是否需要验证协议配置
     * @return 验证结果，包含服务包信息
     */
    private ValidationResult validateAndGetServicePacks(TransmissionConfigReqDTO transmissionConfigReqDTO,
        boolean needValidateProtocolConfig) {
        // 1. 设置租户信息
        Long tenantId = getTenantId(transmissionConfigReqDTO);
        transmissionConfigReqDTO.setTenantId(tenantId);

        // 2. 查询服务包列表
        List<TransmissionServicePackDTO> servicePacks = servicePackService.queryTenantServicePackByNode(
            tenantId,
            transmissionConfigReqDTO.getOrganType(),
            transmissionConfigReqDTO.getNodeType(),
            transmissionConfigReqDTO.getServicePackId(),
            needValidateProtocolConfig);

        // 3. 验证服务包存在性
        boolean isValid = !CollectionUtils.isEmpty(servicePacks);

        // 4. 记录验证日志
        if (!isValid) {
            log.warn("[validateAndGetServicePacks][租户({})未找到节点关联的服务包配置] nodeType:{}",
                tenantId, transmissionConfigReqDTO.getNodeType().getDesc());
        }

        return new ValidationResult(isValid, servicePacks);
    }

    /**
     * 合并配置&执行数据传输 处理配置和业务逻辑
     * <p>
     * 处理流程： 1. 校验配置包：验证配置包的有效性和完整性 2. 合并父配置项：处理配置项的继承关系 3. 执行业务处理：调用具体的业务处理逻辑
     *
     * @param transmissionReqDTO 传输请求对象，包含业务数据
     * @param servicePacks       待处理的服务包列表
     * @param clazz              响应数据类型
     * @return 所有服务包的处理响应列表
     */
    private <T> List<TransmissionRespDTO<T>> handleTransmission(
        TransmissionReqDTO transmissionReqDTO,
        List<TransmissionServicePackDTO> servicePacks,
        Class<T> clazz) {

        List<TransmissionRespDTO<T>> respList = new ArrayList<>();

        for (TransmissionServicePackDTO servicePack : servicePacks) {
            // 1. 校验配置包
            if (isInvalidConfigPackage(servicePack.getConfigPackage())) {
                log.warn("[processServicePacks][租户({})服务包({})配置为空] nodeType:{}",
                    transmissionReqDTO.getConfig().getTenantId(),
                    servicePack.getId(),
                    transmissionReqDTO.getConfig().getNodeType().getDesc());
                continue;
            }

            // 2. 发起请求
            TransmissionRespDTO<T> resp = handleTransmissionRequest(transmissionReqDTO, servicePack, clazz);
            respList.add(resp);
        }

        return respList;
    }

    /**
     * 执行数据传输
     */
    private <T> TransmissionRespDTO<T> handleTransmissionRequest(
        TransmissionReqDTO transmissionReqDTO,
        TransmissionServicePackDTO servicePack,
        Class<T> clazz) {

        TransmissionRespDTO.TransmissionRespDTOBuilder<T> respDTOBuilder = TransmissionRespDTO
            .fromRequest(transmissionReqDTO);

        // 1. 执行逻辑处理
        CommonResult<ProcessContext> processResult = executeLogicProcessing(transmissionReqDTO, servicePack,
            respDTOBuilder);
        if (processResult == null || !processResult.isSuccess()) {
            return respDTOBuilder.build();
        }

        // 2. 处理传输任务(异步or同步)
        if (transmissionReqDTO.isAsync()) {
            handleTaskAsync(processResult.getData().getTaskRecord(), transmissionReqDTO, respDTOBuilder,
                servicePack, clazz);
            respDTOBuilder.result(CommonResult.success(null));
        } else {
            handleTask(processResult.getData().getTaskRecord(), transmissionReqDTO, respDTOBuilder, servicePack,
                clazz);
        }
        return respDTOBuilder.build();
    }

    /**
     * 执行逻辑处理流程
     * <p>
     * 处理流程： 1. 获取逻辑处理器 2. 前置处理：参数校验和数据准备 3. 任务创建：构建任务实体 4. 后置处理：结果处理和清理
     *
     * @param transmissionReqDTO 传输请求对象
     * @param servicePack        服务包信息
     * @param respDTOBuilder     响应构建器
     * @return 处理上下文，如果处理失败返回null
     */
    private <T> CommonResult<ProcessContext> executeLogicProcessing(
        TransmissionReqDTO transmissionReqDTO,
        TransmissionServicePackDTO servicePack,
        TransmissionRespDTO.TransmissionRespDTOBuilder<T> respDTOBuilder) {

        // 1. 获取逻辑处理器
        LogicConfigProcessor processor = logicConfigProcessorFactory.getProcessor(
            transmissionReqDTO.getConfig().getNodeType());

        // 2. 前置处理
        CommonResult<ProcessContext> preValidResult = processor.preProcess(transmissionReqDTO, servicePack);
        if (shouldTerminateProcessing(preValidResult, respDTOBuilder)) {
            return null;
        }

        // 3. 任务创建
        CommonResult<ProcessContext> createTaskResult = processor.createTask(preValidResult.getData());
        if (shouldTerminateProcessing(createTaskResult, respDTOBuilder)) {
            return null;
        }

        // 4. 后置处理
        CommonResult<ProcessContext> postProcessResult = processor.postProcess(createTaskResult.getData());
        if (shouldTerminateProcessing(postProcessResult, respDTOBuilder)) {
            return null;
        }

        return postProcessResult;
    }

    // ===================== 6. 任务处理 =====================


    /**
     * 处理任务 统一的任务处理入口
     * <p>
     * 处理流程： 1. 组装请求参数 2. 发起远程调用 3. 更新任务状态 4. 处理下游请求
     *
     * @param taskRecord         任务记录
     * @param transmissionReqDTO 请求对象
     * @param respDTOBuilder     响应构建器
     * @param servicePack        服务包信息
     * @param clazz              响应数据类型
     */
    private <T> void handleTask(TransmissionTaskRecordSaveReqVO taskRecord,
        TransmissionReqDTO transmissionReqDTO,
        TransmissionRespDTO.TransmissionRespDTOBuilder<T> respDTOBuilder,
        TransmissionServicePackDTO servicePack,
        Class<T> clazz) {
        log.info("开始处理任务，任务ID：{}", taskRecord.getId());

        //判断任务是否为请求中状态（任务未到执行时间 或 上游任务未执行成功）
        if (RequestStatusEnum.IN_PROGRESS.getCode() != taskRecord.getRequestStatus()) {
            log.warn("任务未到执行时间 或 上游任务未执行成功，任务ID：{}", taskRecord.getId());
            handleSuccessResponse(taskRecord, respDTOBuilder, null);
            return;
        }

        try {
            // 1. 组装请求参数
            Map<String, Object> requestParams = buildRequestParams(taskRecord, servicePack, transmissionReqDTO);
            respDTOBuilder.requestParams(requestParams);
            // todo 建议采用远程调用返回的请求三方的参数信息
            taskRecord.setRequestParams(JSON.toJSONString(requestParams, TransmitterConstants.propertyFilter));

            // 2. 发起远程调用
            taskRecord.setActualTime(LocalDateTime.now());
            T responseResult = executeRemoteCall(taskRecord.getTenantId(), servicePack, requestParams, clazz);

            //3. 处理返回结果
            ContractInvokeContext context = ContractInvokeContextHolder.getContext();
            taskRecord.setResponseResult(JSON.toJSONString(context.getInvokeLogs()));

            if (context.isSuccess()) {
                // 3.1. 处理成功响应
                handleSuccessResponse(taskRecord, respDTOBuilder, responseResult);
                // 出参放入下游扩展信息中,兼容存在下游任务得场景
                transmissionReqDTO.getAux().put(taskRecord.getNodeType().toString(), responseResult);
            } else {
                // 3.2. 处理失败响应
                handleFailureResponse(taskRecord, respDTOBuilder, null);
            }
            log.info("任务处理成功，任务ID：{}", taskRecord.getId());
        } catch (Exception e) {
            // 4. 处理失败响应
            handleFailureResponse(taskRecord, respDTOBuilder, e);
        } finally {
            //清除记录请求日志上下文
            ContractInvokeContextHolder.getContext().clear();
            // 5. 更新任务状态
            log.info("更新任务状态，任务ID：{}，状态：{}", taskRecord.getId(), taskRecord.getRequestStatus());
            updateTaskStatus(taskRecord);
        }

        // 6. 处理下游请求
        if (shouldProcessDownstream(transmissionReqDTO, taskRecord)) {
            handleDownstreamRequests(transmissionReqDTO, respDTOBuilder, clazz);
        }
    }

    @Async("transmissionAsyncExecutor") // 确保配置了 @EnableAsync
    public <T> void handleTaskAsync(TransmissionTaskRecordSaveReqVO taskRecord,
        TransmissionReqDTO transmissionReqDTO,
        TransmissionRespDTOBuilder<T> respDTOBuilder,
        TransmissionServicePackDTO servicePack,
        Class<T> clazz) {
        // 直接处理任务，不需要再嵌套一层 CompletableFuture.runAsync
        handleTask(taskRecord, transmissionReqDTO, respDTOBuilder, servicePack, clazz);
    }

    /**
     * 处理同步任务 执行任务并等待结果返回
     */
    // private void handleSyncTask(TransmissionTaskRecordSaveReqVO taskRecord,
    // TransmissionRespDTO.TransmissionRespDTOBuilder<?> respDTOBuilder) {
    // // ... 实现代码 ...
    // }

    /**
     * 处理异步任务 仅返回任务ID，不等待执行结果
     */
    // private void handleAsyncTask(TransmissionTaskRecordSaveReqVO taskRecord,
    // TransmissionRespDTO.TransmissionRespDTOBuilder<?> respDTOBuilder) {
    // respDTOBuilder.taskId(taskRecord.getId())
    // .result(CommonResult.success(null));
    // }

    /**
     * 处理下游请求 递归处理所有下游节点的请求
     */
    private <T> void handleDownstreamRequests(TransmissionReqDTO transmissionReqDTO,
        TransmissionRespDTO.TransmissionRespDTOBuilder<T> respDTOBuilder,
        Class<T> clazz) {
        if (CollectionUtils.isEmpty(transmissionReqDTO.getDownstreamReqList())) {
            return;
        }

        List<TransmissionRespDTO<T>> downstreamRespList = new ArrayList<>();
        for (TransmissionReqDTO downstreamReqDTO : transmissionReqDTO.getDownstreamReqList()) {
            // 填充下游任务扩展信息
            downstreamReqDTO.setAux(transmissionReqDTO.getAux());
            // 如果当前任务是异步的，下游任务也设置为异步
            List<TransmissionRespDTO<T>> transmissionRespDTOS = contractsInvoke(downstreamReqDTO,
                clazz);
            downstreamRespList.addAll(transmissionRespDTOS);
        }
        respDTOBuilder.downstreamRespList(downstreamRespList);
    }

    // ===================== 7. 工具方法 =====================

    /**
     * 构建请求参数 组装远程调用所需的请求参数
     * <p>
     * 处理流程： 1. 参数初始化：创建参数容器 2. 基础参数：设置通用参数 3. 业务参数：设置业务相关参数 4. 扩展参数：设置额外的配置参数
     * <p>
     * 参数来源： - 任务记录：任务相关的基本信息 - 响应构建器：中间处理的结果数据 - 配置信息：系统配置的参数
     *
     * @param taskRecord         任务记录
     * @param servicePack        服务包信息
     * @param transmissionReqDTO 请求对象
     * @return 组装后的请求参数Map
     */
    private Map<String, Object> buildRequestParams(TransmissionTaskRecordSaveReqVO taskRecord,
        TransmissionServicePackDTO servicePack,
        TransmissionReqDTO transmissionReqDTO) {

        // 1. 获取原始请求数据（包含额外组装的参数）
        Map<String, Object> params = transmissionReqDTO.getOriginalParams();

        // 2. 添加上游任务响应结果
        if (taskRecord.getUpstreamTaskId() != null) {
            TransmissionTaskRecordRespVO upstreamTask = taskRecordService
                .getTransmissionTaskRecord(taskRecord.getUpstreamTaskId());
            if (upstreamTask != null && StringUtils.isNotBlank(upstreamTask.getResponseResult())) {
                params.put(CONTRACT_INVOKE_PARAM_UPSTREAM_RESPONSE, parseJsonValue(upstreamTask.getResponseResult()));
            }
        }

        // 3. 添加网络配置信息
        if (CollectionUtils.isNotEmpty(servicePack.getOrgan().getNetworkConfig())) {
            params.put(CONTRACT_INVOKE_PARAM_NETWORK, servicePack.getOrgan().getNetworkConfig().getFirst());
        }
        return params;
    }

    /**
     * 解析JSON值 根据JSON内容类型选择合适的解析方式
     */
    private Object parseJsonValue(String jsonStr) {
        try {
            JsonNode jsonNode = JsonUtils.parseTree(jsonStr);
            if (jsonNode.isArray()) {
                // 如果是数组，解析为List
                return JsonUtils.parseObject(jsonStr, new TypeReference<List<Object>>() {
                });
            } else if (jsonNode.isObject()) {
                // 如果是对象，解析为Map
                return JsonUtils.parseObject(jsonStr, new TypeReference<Map<String, Object>>() {
                });
            } else {
                // 如果是基本类型，直接返回值
                return jsonNode.asText();
            }
        } catch (Exception e) {
            log.error("[parseJsonValue][解析JSON失败] jsonStr:{}", jsonStr, e);
            return jsonStr; // 解析失败时返回原始字符串
        }
    }

    // ===================== 内部类定义 =====================

    /**
     * 验证结果 - 值对象 封装服务包验证的结果信息
     * <p>
     * 属性说明： 1. valid：验证是否通过 2. servicePacks：验证通过的服务包列表
     * <p>
     * 使用场景： 1. 服务包验证：记录验证结果 2. 结果传递：在方法间传递验证信息 3. 状态维护：维护验证过程中的状态
     */
    @Data
    @AllArgsConstructor
    private static class ValidationResult {

        /**
         * 验证是否通过
         */
        private final boolean valid;

        /**
         * 验证通过的服务包列表
         */
        private final List<TransmissionServicePackDTO> servicePacks;
    }

    /**
     * 获取租户ID 如果请求中未指定租户ID，则从上下文中获取
     *
     * @param transmissionConfigReqDTO 传输请求配置信息
     * @return 租户ID
     */
    private Long getTenantId(TransmissionConfigReqDTO transmissionConfigReqDTO) {
        return Optional.ofNullable(transmissionConfigReqDTO.getTenantId())
            .orElse(TenantContextHolder.getRequiredTenantId());
    }

    /**
     * 处理逻辑处理结果 判断逻辑处理是否需要终止
     * <p>
     * 终止条件： 1. 处理成功但无数据返回 2. 处理失败需要终止 3. 达到最大重试次数
     * <p>
     * 处理逻辑： - 成功无数据：设置空响应 - 处理失败：设置错误响应 - 继续处理：返回false
     *
     * @param logicProcessingResult 逻辑处理结果
     * @param respDTOBuilder        响应构建器
     * @return 是否需要终止处理
     */
    private <T> boolean shouldTerminateProcessing(CommonResult<ProcessContext> logicProcessingResult,
        TransmissionRespDTO.TransmissionRespDTOBuilder<T> respDTOBuilder) {
        // 处理成功但没有数据，需要终止
        if (logicProcessingResult.isSuccess() && Objects.isNull(logicProcessingResult.getData())) {
            respDTOBuilder.result(CommonResult.success(null));
            return true;
        }

        // 处理失败，需要终止
        if (!logicProcessingResult.isSuccess()) {
            respDTOBuilder.result(CommonResult.error(logicProcessingResult));
            return true;
        }

        // 处理成功且有数据，继续处理
        return false;
    }

    /**
     * 判断是否需要处理下游请求 根据任务状态和配置判断是否继续处理
     * <p>
     * 判断条件： 1. 当前任务是否执行成功 2. 是否配置了下游处理 3. 是否达到最大处理深度
     *
     * @param taskRecord         任务记录
     * @param transmissionReqDTO 请求对象
     * @return 是否需要处理下游请求
     */
    private boolean shouldProcessDownstream(TransmissionReqDTO transmissionReqDTO,
        TransmissionTaskRecordSaveReqVO taskRecord) {
        // TODO: 根据任务状态和配置判断是否需要处理下游请求
        // if (transmissionReqDTO.isRetry()) {
        //     return false;
        // }
        if (taskRecord.getRequestStatus().equals(RequestStatusEnum.SUCCESS.getCode())) {
            return true;
        }
        return false;
    }

    /**
     * 解析YAML格式的配置字符串为ContractConfig对象
     *
     * @param yamlConfig YAML格式的配置字符串
     * @throws RuntimeException 如果配置解析失败或配置类型不正确
     */
    private ContractConfig parseProtocolConfig(String yamlConfig, String commonConfig) {
        try {
            StringConfigReader reader = new StringConfigReader();
            DSLConfig config = reader.readerConfig(null, yamlConfig, commonConfig);

            return (ContractConfig) config;
        } catch (Exception e) {
            throw exception(formatError(TRANSMISSION_PROTOCOL_CONFIG_PARSE_FAILED, e));
        }
    }

    /**
     * 处理成功响应 处理远程调用成功的情况
     * <p>
     * 处理内容： 1. 更新任务状态为成功 2. 保存响应结果 3. 清空错误信息 4. 设置响应数据
     *
     * @param taskRecord     任务记录
     * @param respDTOBuilder 响应构建器
     * @param responseResult 调用响应结果
     */
    private <T> void handleSuccessResponse(TransmissionTaskRecordSaveReqVO taskRecord,
        TransmissionRespDTO.TransmissionRespDTOBuilder<T> respDTOBuilder,
        T responseResult) {
        taskRecord.setRequestStatus(RequestStatusEnum.SUCCESS.getCode());
        taskRecord.setResponseResult(StringUtils.defaultIfBlank(taskRecord.getResponseResult(), JSON.toJSONString(responseResult)));
        taskRecord.setErrorMessage("");

        respDTOBuilder.result(CommonResult.success(responseResult));
    }

    /**
     * 处理失败响应 处理远程调用失败的情况
     * <p>
     * 处理逻辑： 1. 记录错误日志 2. 判断是否需要重试 3. 更新任务状态 4. 设置错误响应
     * <p>
     * 重试处理： - 检查重试次数 - 更新重试计数 - 设置下次重试时间
     *
     * @param taskRecord     任务记录
     * @param respDTOBuilder 响应构建器
     * @param e              异常信息
     */
    private void handleFailureResponse(TransmissionTaskRecordSaveReqVO taskRecord,
        TransmissionRespDTO.TransmissionRespDTOBuilder<?> respDTOBuilder,
        Exception e) {
        log.error("[handleSyncTask][任务({})执行失败]", taskRecord.getId(), e);
        String errorMessage = "";
        if (e != null) {
            errorMessage = e.getMessage();
        }

        // 判断是否需要重试
        if (shouldRetry(taskRecord)) {
            taskRecord.setRequestStatus(RequestStatusEnum.IN_PROGRESS.getCode());
            taskRecord.setRetryCount(taskRecord.getRetryCount() + 1);

            // todo 自动重传

        } else {
            taskRecord.setRequestStatus(RequestStatusEnum.FAILED.getCode());
        }

        taskRecord.setErrorMessage(errorMessage);
        respDTOBuilder.result(CommonResult.error(TRANSMISSION_REQUEST_FAILED.getCode(), errorMessage));
    }

    /**
     * 判断任务是否需要重试
     *
     * @param taskRecord 任务记录
     * @return 是否需要重试
     */
    private boolean shouldRetry(TransmissionTaskRecordSaveReqVO taskRecord) {
        return taskRecord.getMaxRetryCount() > 0
            && taskRecord.getRetryCount() < taskRecord.getMaxRetryCount();
    }

    /**
     * 更新任务状态 更新任务的执行状态和相关信息
     * <p>
     * 更新内容： 1. 完成时间：设置任务完成时间戳 2. 执行状态：更新任务的执行状态 3. 错误信息：记录执行过程中的错误
     * <p>
     * 异常处理： - 更新失败记录错误日志 - 不影响主流程执行
     *
     * @param taskRecord 任务记录对象
     */
    private void updateTaskStatus(TransmissionTaskRecordSaveReqVO taskRecord) {
        taskRecord.setCompleteTime(LocalDateTime.now());
        try {
            taskRecordService.updateTransmissionTaskRecord(taskRecord);
        } catch (Exception ex) {
            log.error("[handleSyncTask][更新任务状态失败] taskId:{} error:{}",
                taskRecord.getId(), ex.getMessage(), ex);
        }
    }

    /**
     * 统一的业务处理模板方法
     */
    private <T> T executeWithErrorHandling(String methodName, TransmissionReqDTO reqDTO,
        ThrowableSupplier<T> supplier) {
        try {
            return supplier.get();
        } catch (Exception e) {
            log.error("[{}][执行异常] tenantId:{} nodeType:{} error:{}",
                methodName,
                reqDTO.getConfig().getTenantId(),
                reqDTO.getConfig().getNodeType().getDesc(),
                e.getMessage(), e);
            throw exception(formatError(TRANSMISSION_REQUEST_FAILED, e));
        }
    }

    /**
     * 函数式接口，用于处理可能抛出异常的操作
     */
    @FunctionalInterface
    private interface ThrowableSupplier<T> {

        T get() throws Exception;
    }

    /**
     * 处理子任务重传
     */
    // private void handleChildTasksRetry(TransmissionTaskRecordRespVO parentTask) {
    //     // 1. 查询子任务
    //     List<TransmissionTaskRecordRespVO> childTasks = taskRecordService.getTransmissionTaskRecordList(
    //         TransmissionTaskRecordPageReqVO.builder()
    //             .upstreamTaskId(parentTask.getId())
    //             .build());
    //
    //     if (CollectionUtils.isEmpty(childTasks)) {
    //         return;
    //     }
    //
    //     // 2. 重传子任务
    //     for (TransmissionTaskRecordRespVO childTask : childTasks) {
    //         // 构建子任务请求
    //         TransmissionReqDTO childReqDTO = TransmissionReqDTO.builder()
    //             .taskId(childTask.getId())
    //             .build();
    //
    //         // 执行子任务重传
    //         retryContractInvoke(childReqDTO);
    //     }
    // }

    /**
     * 获取协议
     */
    private String getProtocol(TransmissionServicePackDTO servicePack) {
        return servicePack.getConfigPackage().getConfigItems().stream()
            .filter(item -> !item.getDisable() && item.getDslType().equals(DslTypeEnum.PROTOCOL.getCode()))
            .map(TransmissionConfigItemDTO::getConfigValue)
            .findFirst()
            .orElseThrow(() -> exception(TRANSMISSION_PROTOCOL_CONFIG_NOT_EXISTS));
    }

    /**
     * 获取协议
     */
    private String getCommonProtocol(TransmissionServicePackDTO servicePack) {
        if (CollectionUtils.isEmpty(servicePack.getConfigPackage().getCommonConfigItems())) {
            return null;
        }
        return servicePack.getConfigPackage().getCommonConfigItems().stream()
            .filter(item -> !item.getDisable() && item.getDslType().equals(DslTypeEnum.PROTOCOL.getCode()))
            .map(TransmissionConfigItemDTO::getConfigValue)
            .findFirst()
            .orElseThrow(() -> exception(TRANSMISSION_PROTOCOL_CONFIG_NOT_EXISTS));
    }

}