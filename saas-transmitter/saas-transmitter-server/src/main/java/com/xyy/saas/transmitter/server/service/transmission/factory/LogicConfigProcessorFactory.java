package com.xyy.saas.transmitter.server.service.transmission.factory;

import cn.iocoder.yudao.framework.common.exception.enums.GlobalErrorCodeConstants;
import com.xyy.saas.inquiry.enums.transmitter.NodeTypeEnum;
import com.xyy.saas.inquiry.enums.transmitter.OrganTypeEnum;
import com.xyy.saas.transmitter.server.service.transmission.processor.LogicConfigProcessor;
import org.springframework.stereotype.Component;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception0;

/**
 * 逻辑配置处理器工厂类
 * 
 * 功能：
 * 1. 提供获取逻辑配置处理器的接口
 * 2. 根据节点类型返回对应的处理器实例
 * 
 * 设计原则：
 * 1. 单一职责：负责处理器的创建和管理
 * 2. 开闭原则：支持扩展新的节点类型处理器
 * 
 * 使用场景：
 * 1. 在数据传输过程中，根据节点类型获取对应的业务处理器
 * 2. 支持多种业务节点的处理逻辑
 * 
 * 示例：
 * LogicConfigProcessor processor = factory.getProcessor(nodeType);
 * 
 */
@Slf4j
@Component
public class LogicConfigProcessorFactory {

    private final Map<NodeTypeEnum, LogicConfigProcessor> processorMap;
    private final Map<OrganTypeEnum, LogicConfigProcessor> organTypeProcessorMap;

    /**
     * 构造函数中初始化所有处理器
     */
    public LogicConfigProcessorFactory(List<LogicConfigProcessor> processors) {
        this.processorMap = new ConcurrentHashMap<>();
        this.organTypeProcessorMap = new ConcurrentHashMap<>();
        initProcessors(processors);
    }

    private void initProcessors(List<LogicConfigProcessor> processors) {
        for (LogicConfigProcessor processor : processors) {
            // 1. 注册具体业务点类型的处理器
            NodeTypeEnum nodeType = processor.getNodeType();
            if (nodeType != null) {
                processorMap.put(nodeType, processor);
                continue;
            }

            // 2. 注册机构类型的处理器
            OrganTypeEnum organType = processor.getOrganType();
            if (organType != null) {
                organTypeProcessorMap.put(organType, processor);
            }
        }

        log.info("[initProcessors][初始化处理器完成] nodeTypeSize:{} organTypeSize:{}",
                processorMap.size(), organTypeProcessorMap.size());
    }

    /**
     * 获取逻辑配置处理器
     * 
     * 根据节点类型返回对应的处理器实例
     * 
     * @param nodeType 节点类型
     * @return 对应的逻辑配置处理器
     * @throws IllegalArgumentException 如果节点类型不支持
     */
    public LogicConfigProcessor getProcessor(NodeTypeEnum nodeType) {
        // 1. 优先获取具体业务点类型的处理器
        LogicConfigProcessor processor = processorMap.get(nodeType);
        if (processor != null) {
            return processor;
        }

        // 2. 获取机构类型的处理器
        processor = organTypeProcessorMap.get(nodeType.getOrganType());
        if (processor != null) {
            return processor;
        }

        log.error(String.format("未找到对应的处理器实现类: nodeType=%s(%d), organType=%s",
                nodeType.getDesc(), nodeType.getCode(), nodeType.getOrganType().getDesc()));
        throw exception0(GlobalErrorCodeConstants.FAIL.getCode(), "未找到对应的处理器实现类");
    }
}