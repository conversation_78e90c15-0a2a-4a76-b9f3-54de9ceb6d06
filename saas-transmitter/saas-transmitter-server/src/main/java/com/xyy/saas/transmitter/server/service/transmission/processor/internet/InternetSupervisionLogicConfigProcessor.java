package com.xyy.saas.transmitter.server.service.transmission.processor.internet;

import com.xyy.saas.inquiry.enums.transmitter.OrganTypeEnum;
import com.xyy.saas.transmitter.server.service.transmission.processor.LogicConfigProcessor;
import com.xyy.saas.transmitter.server.service.transmission.processor.TransmissionPrescriptionService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

/**
 * 互联网监管 基础处理器 提供 互联网监管通用的处理逻辑
 * <p>
 * 核心功能： 1. 通用参数处理 2. 基础校验逻辑 3.  互联网监管特定配置处理
 */
@Component
public abstract class InternetSupervisionLogicConfigProcessor extends LogicConfigProcessor {

    @Override
    public OrganTypeEnum getOrganType() {
        return OrganTypeEnum.INTERNET_SUPERVISION;
    }

    @Resource
    protected TransmissionPrescriptionService transmissionPrescriptionService;


}