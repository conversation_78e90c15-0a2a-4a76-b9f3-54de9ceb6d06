package com.xyy.saas.inquiry.hospital.server.mq.consumer.prescription;

import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.iocoder.yudao.framework.common.enums.CommonStatusEnum;
import cn.iocoder.yudao.module.infra.api.config.ConfigApi;
import cn.iocoder.yudao.module.system.api.tenant.TenantApi;
import com.google.common.collect.Lists;
import com.xyy.saas.eventbus.rocketmq.annotation.EventBusConsumer;
import com.xyy.saas.eventbus.rocketmq.annotation.EventBusListener;
import com.xyy.saas.inquiry.constant.PrescriptionConstant;
import com.xyy.saas.inquiry.enums.tenant.TenantTypeEnum;
import com.xyy.saas.inquiry.hospital.server.service.prescription.RemoteAuditPrescriptionService;
import com.xyy.saas.inquiry.mq.tenant.TenantInfoUpdateEvent;
import com.xyy.saas.inquiry.mq.tenant.TenantInfoUpdateMessageDto;
import com.xyy.saas.inquiry.patient.api.inquiry.InquiryRemoteAuditApi;
import com.xyy.saas.inquiry.pojo.TenantDto;
import jakarta.annotation.Resource;
import java.util.List;
import java.util.concurrent.TimeUnit;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

/**
 * @Author: xucao
 * @DateTime: 2025/4/24 14:21
 * @Description: 租户信息变更，远程审方业务监听消费者
 **/
@Component
@Slf4j
@EventBusConsumer(groupId = "com_xyy_saas_inquiry_hospital_server_mq_consumer_prescription_TenantInfoUpdateRemoteAuditConsumer", topic = TenantInfoUpdateEvent.TOPIC)
public class TenantInfoUpdateRemoteAuditConsumer {

    @DubboReference
    private TenantApi tenantApi;

    @Resource
    private RemoteAuditPrescriptionService remoteAuditPrescriptionService;

    @DubboReference
    private InquiryRemoteAuditApi inquiryRemoteAuditApi;

    @DubboReference
    private ConfigApi configApi;

    @EventBusListener
    public void tenantUpdRemoteAuditOnMessage(TenantInfoUpdateEvent event) {
        TenantInfoUpdateMessageDto msg = event.getMsg();
        // 查询当前门店信息
        TenantDto tenant = tenantApi.getTenant(msg.getId());
        // 无需处理条件 1. 门店类型和连锁总部未变更 2. 门店状态未变更 3. 门店状态变更但是当前状态为正常
        boolean noNeedHandle =
            ObjectUtil.equals(tenant.getWzTenantType(), TenantTypeEnum.fromCode(msg.getWzTenantType())) && ObjectUtil.equals(tenant.getHeadTenantId(), msg.getHeadTenantId()) && (ObjectUtil.equals(tenant.getStatus(), msg.getStatus())
                || ObjectUtil.equals(
                tenant.getStatus(), CommonStatusEnum.ENABLE.getStatus()));
        if (noNeedHandle) {
            log.info("门店类型、连锁总部、门店状态均未变更，远程审方无需处理");
            return;
        }
        // 查询门店所有待审核的远程审方处方编号
        List<String> inquriyPrefList = remoteAuditPrescriptionService.selectRemoteAuditInquriyPrefList(msg.getId());
        if (CollectionUtils.isEmpty(inquriyPrefList)) {
            log.info("门店无待审核的远程审方处方，无需处理");
            return;
        }
        Lists.partition(inquriyPrefList, NumberUtil.parseInt(configApi.getConfigValueByKey(PrescriptionConstant.REMOTE_PRESCRIPTION_BATCH_CANCEL_PAGE_SIZE), 100)).forEach(prefList -> {
            // 调问诊api 批量取消
            inquiryRemoteAuditApi.batchCancelRemoteInquiry(prefList);
            try {
                TimeUnit.MILLISECONDS.sleep(NumberUtil.parseInt(configApi.getConfigValueByKey(PrescriptionConstant.REMOTE_PRESCRIPTION_BATCH_CANCEL_INTERVAL), 500));
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
        });
    }
}
