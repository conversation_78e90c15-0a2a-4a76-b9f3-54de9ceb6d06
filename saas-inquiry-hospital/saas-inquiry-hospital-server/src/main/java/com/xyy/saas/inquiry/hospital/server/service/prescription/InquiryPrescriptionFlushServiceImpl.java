package com.xyy.saas.inquiry.hospital.server.service.prescription;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.collection.CollectionUtils;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.tenant.core.util.TenantUtils;
import com.alibaba.fastjson.JSON;
import com.xyy.saas.inquiry.drugstore.api.tenant.TenantParamConfigApi;
import com.xyy.saas.inquiry.enums.inquiry.InquiryBizTypeEnum;
import com.xyy.saas.inquiry.enums.signature.ContractTypeEnum;
import com.xyy.saas.inquiry.hospital.api.doctor.dto.InquiryDoctorDto;
import com.xyy.saas.inquiry.hospital.api.prescription.dto.InquiryPrescriptionDetailRespDTO;
import com.xyy.saas.inquiry.hospital.api.prescription.dto.InquiryPrescriptionRespDTO;
import com.xyy.saas.inquiry.hospital.api.prescription.dto.InquiryPrescriptionUrlDTO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.prescription.vo.InquiryPrescriptionFlushReqVO;
import com.xyy.saas.inquiry.hospital.server.convert.prescription.InquiryPrescriptionConvert;
import com.xyy.saas.inquiry.hospital.server.convert.prescription.InquiryPrescriptionDetailConvert;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.prescription.InquiryPrescriptionDO;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.prescription.InquiryPrescriptionDetailDO;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.prescription.InquiryPrescriptionUrlDO;
import com.xyy.saas.inquiry.hospital.server.dal.mysql.doctor.DoctorPracticeMapper;
import com.xyy.saas.inquiry.hospital.server.dal.mysql.doctor.InquiryDoctorMapper;
import com.xyy.saas.inquiry.hospital.server.dal.mysql.prescription.InquiryPrescriptionDetailMapper;
import com.xyy.saas.inquiry.hospital.server.dal.mysql.prescription.InquiryPrescriptionMapper;
import com.xyy.saas.inquiry.hospital.server.dal.mysql.prescription.InquiryPrescriptionUrlMapper;
import com.xyy.saas.inquiry.patient.api.inquiry.InquiryApi;
import com.xyy.saas.inquiry.patient.api.inquiry.dto.InquiryRecordDetailDto;
import com.xyy.saas.inquiry.signature.api.prescription.dto.PrescriptionParamDto;
import com.xyy.saas.inquiry.signature.api.signature.InquirySignatureContractApi;
import com.xyy.saas.inquiry.signature.api.signature.dto.InquiryFlushContractReqDto;
import com.xyy.saas.inquiry.signature.api.signature.dto.InquiryFlushContractRespDto;
import com.xyy.saas.inquiry.signature.api.signature.dto.InquirySignatureContractDto;
import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.ThreadPoolExecutor.CallerRunsPolicy;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 刷处方 Service 接口
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class InquiryPrescriptionFlushServiceImpl implements InquiryPrescriptionFlushService {

    @DubboReference
    private InquiryApi inquiryApi;

    @Resource
    private InquiryDoctorMapper inquiryDoctorMapper;

    private DoctorPracticeMapper doctorPracticeMapper;

    @Resource
    private InquiryPrescriptionUrlMapper inquiryPrescriptionUrlMapper;

    @Resource
    private TenantParamConfigApi tenantParamConfigApi;

    @Resource
    private InquirySignatureContractApi inquirySignatureContractApi;

    @Resource
    private InquiryPrescriptionMapper inquiryPrescriptionMapper;

    @Resource
    private InquiryPrescriptionDetailMapper inquiryPrescriptionDetailMapper;

    // 使用静态内部类实现线程安全
    private static final ThreadPoolExecutor FLUSH_POOL_EXECUTOR = new ThreadPoolExecutor(
        // 核心线程数设置为 CPU 核心数
        Runtime.getRuntime().availableProcessors(),
        // 最大线程数设置为 CPU 核心数 * 2
        Runtime.getRuntime().availableProcessors() * 2,
        // 空闲线程存活时间 120 秒
        120, TimeUnit.SECONDS,
        // 队列容量 200（避免过大导致内存问题）
        new LinkedBlockingQueue<>(200),
        // 自定义线程工厂（命名线程便于监控）
        new ThreadFactory() {
            private final AtomicInteger threadCount = new AtomicInteger(1);

            @Override
            public Thread newThread(Runnable r) {
                Thread thread = new Thread(r, "prescription-flush-" + threadCount.getAndIncrement());
                thread.setDaemon(false); // 非守护线程
                return thread;
            }
        },
        // 调用线程执行
        new CallerRunsPolicy()
    );

    /**
     * 刷处方开关
     */
    @Value("${prescription.flush.switch:true}")
    private boolean prescriptionFlushSwitch;

    /**
     * 刷处方分页大小 默认50条
     */
    @Value("${prescription.flush.pageSize:50}")
    private Integer prescriptionFlushPageSize;

    /**
     * 刷处方间隔时间 默认1500毫秒
     */
    @Value("${prescription.flush.interval:1500}")
    private Integer prescriptionFlushInterval;


    @Override
    public InquiryPrescriptionUrlDTO flushSinglePrescription(String pref) {
        return null;
    }

    @Override
    @Transactional
    public InquiryPrescriptionUrlDTO flushPrescriptionPrice(String pref) {
        InquiryPrescriptionDO prescriptionDO = inquiryPrescriptionMapper.selectOne(InquiryPrescriptionDO::getPref, pref);
        if (prescriptionDO == null) {
            return null;
        }
        InquiryPrescriptionUrlDTO prescriptionUrlDTO = TenantUtils.execute(prescriptionDO.getTenantId()
            , () -> executeFlushSinglePrescription(prescriptionDO, null, null));

        // 划价变更后 更新ext + url
        inquiryPrescriptionMapper.updateById(InquiryPrescriptionDO.builder().id(prescriptionDO.getId())
            .prescriptionPdfUrl(prescriptionUrlDTO.getPrescriptionPdfUrl())
            .prescriptionImgUrl(prescriptionUrlDTO.getPrescriptionImgUrl())
            .ext(prescriptionDO.extGet()).build());

        // 删除处方扩展图片
        inquiryPrescriptionMapper.deleteByPref(prescriptionDO.getPref());

        return prescriptionUrlDTO;
    }


    @Override
    public List<InquiryPrescriptionDO> getPrescriptionsHandleDateType(List<InquiryPrescriptionDO> prescriptionDOS) {
        if (CollUtil.isEmpty(prescriptionDOS)) {
            return prescriptionDOS;
        }

        // 1. 使用线程安全的集合
        List<InquiryPrescriptionDO> updateDoList = Collections.synchronizedList(new ArrayList<>());

        List<InquiryPrescriptionUrlDO> insertUrlList = Collections.synchronizedList(new ArrayList<>());

        // 2. 收集需要异步处理的任务
        List<CompletableFuture<Void>> futures = new ArrayList<>();

        // 3. 获取租户配置
        Integer dateType = tenantParamConfigApi.getTenantPresDateType(prescriptionDOS.getFirst().getTenantId());

        // 4. 处方图片
        Map<String, InquiryPrescriptionUrlDO> prescriptionUrlMap = inquiryPrescriptionUrlMapper.selectByPrefMap(prescriptionDOS.stream().map(InquiryPrescriptionDO::getPref).collect(Collectors.toList()), dateType);

        for (InquiryPrescriptionDO p : prescriptionDOS) {
            //  过滤 非药店购药处方 或 类型一致的
            if (!Objects.equals(p.getInquiryBizType(), InquiryBizTypeEnum.DRUGSTORE_INQUIRY.getCode()) || Objects.equals(p.extGet().getInquiryPresDateType(), dateType)) {
                continue;
            }

            // 存在则设置图片出参
            InquiryPrescriptionUrlDO urlDO = prescriptionUrlMap.get(p.getPref());
            if (urlDO != null) {
                p.setPrescriptionPdfUrl(urlDO.getPrescriptionPdfUrl()).setPrescriptionImgUrl(urlDO.getPrescriptionImgUrl());
                continue;
            }

            //  需要重新生成处方笺
            futures.add(CompletableFuture.runAsync(() -> {
                try {
                    Integer presDateType = p.extGet().getInquiryPresDateType();
                    // 重新生成 设置到ext中
                    InquiryPrescriptionUrlDTO pu = TenantUtils.execute(p.getTenantId(), () -> executeFlushSinglePrescription(p, null, null));

                    // 原类型不为空,才设置extUrl。为空补充ext-DateType
                    if (presDateType != null) {
                        insertUrlList.add(InquiryPrescriptionUrlDO.builder().pref(p.getPref()).dateType(dateType).prescriptionPdfUrl(pu.getPrescriptionPdfUrl()).prescriptionImgUrl(pu.getPrescriptionImgUrl()).build());
                    } else {
                        updateDoList.add(InquiryPrescriptionDO.builder().id(p.getId()).prescriptionPdfUrl(pu.getPrescriptionPdfUrl()).prescriptionImgUrl(pu.getPrescriptionImgUrl()).ext(p.extGet()).build());
                    }
                    // 设置出参图片
                    p.setPrescriptionPdfUrl(pu.getPrescriptionPdfUrl()).setPrescriptionImgUrl(pu.getPrescriptionImgUrl());

                } catch (Exception e) {
                    log.error("处方重新生成失败，处方pref: {}", p.getPref(), e);
                }
            }, FLUSH_POOL_EXECUTOR));
        }

        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();

        if (CollUtil.isNotEmpty(updateDoList)) {
            inquiryPrescriptionMapper.updateById(updateDoList);
        }

        if (CollUtil.isNotEmpty(insertUrlList)) {
            inquiryPrescriptionUrlMapper.insertBatch(insertUrlList);
        }

        return prescriptionDOS;
    }


    @Override
    public void flushInquiryPrescription(InquiryPrescriptionFlushReqVO flushReqVO) {

        log.info("刷处方数据开始----vo:{}", JSON.toJSONString(flushReqVO));

        long flushCount = 0;

        for (int i = 0; i < 1000; i++) {

            if (!prescriptionFlushSwitch) {
                return;
            }

            PageResult<InquiryPrescriptionDO> prescriptionList = getFlushPrescriptionList(flushReqVO);
            List<InquiryPrescriptionDO> list = prescriptionList.getList();
            if (CollUtil.isEmpty(list)) {
                break;
            }
            // 设置最大id
            list.stream().mapToLong(InquiryPrescriptionDO::getId).max().ifPresent(flushReqVO::setMaxId);

            flushCount += list.size();
            log.info("刷处方数据进度,次数:{},已刷数/总数:{} / {}", i, flushCount, prescriptionList.getTotal());
            // 处方详情map
            Map<String, List<InquiryPrescriptionDetailDO>> detailMap = inquiryPrescriptionDetailMapper.selectByPrescriptionPrefs(CollectionUtils.convertList(list, InquiryPrescriptionDO::getPref))
                .stream().collect(Collectors.groupingBy(InquiryPrescriptionDetailDO::getPrescriptionPref));
            // 问诊map
            Map<String, InquiryRecordDetailDto> recordMap = inquiryApi.getInquiryRecordDetails(CollectionUtils.convertList(list, InquiryPrescriptionDO::getInquiryPref))
                .stream().collect(Collectors.toMap(InquiryRecordDetailDto::getInquiryPref, Function.identity(), (a, b) -> b));

            for (InquiryPrescriptionDO prescriptionDO : list) {

                if (!prescriptionFlushSwitch) {
                    return;
                }
                // 循环一次停默认ms
                try {
                    TimeUnit.MILLISECONDS.sleep(prescriptionFlushInterval);
                } catch (Exception ignore) {
                }
                // 执行单个刷处方
                FLUSH_POOL_EXECUTOR.execute(() -> {
                    try {
                        InquiryPrescriptionUrlDTO prescriptionUrlDTO = TenantUtils.execute(prescriptionDO.getTenantId(),
                            () -> executeFlushSinglePrescription(prescriptionDO, detailMap.get(prescriptionDO.getPref()), recordMap.get(prescriptionDO.getInquiryPref())));

                        // 处理更新处方图片
                        inquiryPrescriptionMapper.updateById(InquiryPrescriptionDO.builder().id(prescriptionDO.getId())
                            .prescriptionPdfUrl(prescriptionUrlDTO.getPrescriptionPdfUrl())
                            .prescriptionImgUrl(prescriptionUrlDTO.getPrescriptionImgUrl())
                            .ext(prescriptionDO.getExt()).build());

                        // 删除处方扩展图片
                        inquiryPrescriptionMapper.deleteByPref(prescriptionDO.getPref());

                    } catch (Exception e) {
                        log.error("刷处方数据失败,pref:{}", prescriptionDO.getPref(), e);
                    }
                });
            }
        }

        log.info("刷处方数据结束----总数量:{}", flushCount);
    }


    /**
     * 执行刷单条处方数据 支持：1.补充确实的参与方签名图片，2.商品信息修改
     * <p>仅刷处方相关时, 问诊详情和处方明细可传空,则处方笺仅修改处方上相关信息,比如时间、价格</p>
     *
     * @param prescriptionDO
     * @param inquiryPrescriptionDetailDOS
     */
    public InquiryPrescriptionUrlDTO executeFlushSinglePrescription(InquiryPrescriptionDO prescriptionDO
        , List<InquiryPrescriptionDetailDO> inquiryPrescriptionDetailDOS, InquiryRecordDetailDto inquiryRecordDto) {

        InquirySignatureContractDto contract = inquirySignatureContractApi.getSignaturePlatformContract(prescriptionDO.getPref(), ContractTypeEnum.PRESCRIPTION);
        if (contract == null) {
            return null;
        }

        // 转换处方、就诊、商品详情等参数
        PrescriptionParamDto paramDto = convertPrescriptionParamDto(prescriptionDO, inquiryRecordDto, inquiryPrescriptionDetailDOS);

        // 忽略源对象的 null 值 不覆盖目标对象已有值（即仅当目标字段为 null 时才设置）
        PrescriptionParamDto oriParamDto = JSONUtil.toBean(contract.getParamDetail(), PrescriptionParamDto.class);
        BeanUtil.copyProperties(oriParamDto, paramDto, CopyOptions.create().setIgnoreNullValue(true).setOverride(false));

        // 重新生成处方合同
        CommonResult<InquiryFlushContractRespDto> flushContractRes = inquirySignatureContractApi.flushContract(
            InquiryFlushContractReqDto.builder().pref(contract.getPref()).tenantId(prescriptionDO.getTenantId()).paramDto(paramDto).build());

        log.info("刷处方数据pref:{},pdf:{}", prescriptionDO.getPref(), flushContractRes.getData());

        return new InquiryPrescriptionUrlDTO().setId(prescriptionDO.getId()).setPref(prescriptionDO.getPref())
            .setPrescriptionPdfUrl(flushContractRes.isSuccess() ? flushContractRes.getData().getPdfUrl() : prescriptionDO.getPrescriptionPdfUrl())
            .setPrescriptionImgUrl(flushContractRes.isSuccess() ? flushContractRes.getData().getImgUrl() : prescriptionDO.getPrescriptionImgUrl());
    }


    private PrescriptionParamDto convertPrescriptionParamDto(InquiryPrescriptionDO prescriptionDO
        , InquiryRecordDetailDto inquiryRecordDto, List<InquiryPrescriptionDetailDO> inquiryPrescriptionDetailDOS) {

        Integer dateType = tenantParamConfigApi.getTenantPresDateType(prescriptionDO.getTenantId());

        prescriptionDO.extGet().setInquiryPresDateType(dateType);

        // 构建参数相关信息
        InquiryPrescriptionRespDTO prescriptionRespDTO = InquiryPrescriptionConvert.INSTANCE.convertDO2DTO(prescriptionDO);

        List<InquiryPrescriptionDetailRespDTO> detailRespDtos = InquiryPrescriptionDetailConvert.INSTANCE.convertDtos(inquiryPrescriptionDetailDOS);

        PrescriptionParamDto paramDto = InquiryPrescriptionConvert.INSTANCE.convertPrescriptionParamDto(prescriptionRespDTO, inquiryRecordDto, detailRespDtos, new InquiryDoctorDto());

        return paramDto;
    }


    private PageResult<InquiryPrescriptionDO> getFlushPrescriptionList(InquiryPrescriptionFlushReqVO flushReqVO) {

        flushReqVO.setPageSize(prescriptionFlushPageSize);
        flushReqVO.setInquiryBizType(flushReqVO.getInquiryBizType() == null ? InquiryBizTypeEnum.DRUGSTORE_INQUIRY.getCode() : flushReqVO.getInquiryBizType());

        return inquiryPrescriptionMapper.selectPage(flushReqVO, new LambdaQueryWrapperX<InquiryPrescriptionDO>()
            .inIfPresent(InquiryPrescriptionDO::getPref, flushReqVO.getPrefs())
            .gtIfPresent(InquiryPrescriptionDO::getId, flushReqVO.getMaxId())
            .eqIfPresent(InquiryPrescriptionDO::getTenantId, flushReqVO.getTenantId())
            .eqIfPresent(InquiryPrescriptionDO::getStatus, flushReqVO.getStatus())
            .eqIfPresent(InquiryPrescriptionDO::getInquiryBizType, flushReqVO.getInquiryBizType())
            .betweenIfPresent(InquiryPrescriptionDO::getOutPrescriptionTime, flushReqVO.getOutPrescriptionTime())
            .orderByAsc(InquiryPrescriptionDO::getId));
    }
}