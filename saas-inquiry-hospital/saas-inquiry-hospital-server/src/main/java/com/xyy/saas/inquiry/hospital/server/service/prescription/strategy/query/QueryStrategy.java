package com.xyy.saas.inquiry.hospital.server.service.prescription.strategy.query;

import cn.hutool.core.collection.CollUtil;
import cn.iocoder.yudao.framework.tenant.core.context.TenantContextHolder;
import com.xyy.saas.inquiry.hospital.server.constant.QuerySceneEnum;
import com.xyy.saas.inquiry.hospital.server.controller.admin.prescription.vo.InquiryPrescriptionPageReqVO;
import java.util.Collections;
import java.util.List;
import java.util.function.Supplier;

/**
 * @ClassName：QueryStrategy
 * @Author: xucao
 * @Date: 2024/10/30 20:48
 * @Description: 处方查询场景、查询参数设置策略
 */
public interface QueryStrategy {

    /**
     * 设置相关参数
     *
     * @param pageReqVO
     */
    void setParam(InquiryPrescriptionPageReqVO pageReqVO);

    /**
     * 获取当前策略对应的查询场景
     *
     * @return
     */
    QuerySceneEnum getQueryScene();


    /**
     * 如果前端传入了 tenantId 就用前端传入的 tenantId， 如果前端没传入，获取当前登录门店下所有连锁门店的，如果为空,用当前登录用户所属的租户id
     *
     * @param consumer tenantApi::getTenantIdsByHeadId
     * @param tenantId
     * @return
     */
    default List<Long> handleQueryTenantIds(Supplier<List<Long>> consumer, Long tenantId) {
        if (tenantId != null) {
            return Collections.singletonList(tenantId);
        }
        List<Long> tenantIds = consumer.get();
        return CollUtil.isEmpty(tenantIds) ? Collections.singletonList(TenantContextHolder.getRequiredTenantId()) : tenantIds;
    }
}
