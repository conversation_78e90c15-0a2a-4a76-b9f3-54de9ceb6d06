package com.xyy.saas.inquiry.hospital.server.service.prescription.strategy.query;

import static cn.iocoder.yudao.framework.security.core.util.SecurityFrameworkUtils.getLoginUser;

import cn.iocoder.yudao.framework.common.enums.CommonStatusEnum;
import cn.iocoder.yudao.module.system.api.permission.PermissionApi;
import cn.iocoder.yudao.module.system.api.tenant.TenantApi;
import com.xyy.saas.inquiry.enums.inquiry.PrescriptionStatusEnum;
import com.xyy.saas.inquiry.enums.system.RoleCodeEnum;
import com.xyy.saas.inquiry.hospital.server.constant.QuerySceneEnum;
import com.xyy.saas.inquiry.hospital.server.controller.admin.prescription.vo.InquiryPrescriptionPageReqVO;
import com.xyy.saas.inquiry.pharmacist.api.pharmacist.InquiryPharmacistApi;
import jakarta.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @Author: xucao
 * @Date: 2024/10/31 9:53
 * @Description: 审方记录 - 药师或门店查询处方审方记录 参数设置策略
 */
@Component
public class PharmacistQueryStrategy implements QueryStrategy {

    @Resource
    private InquiryPharmacistApi inquiryPharmacistApi;

    @Resource
    private PermissionApi permissionApi;

    @Autowired
    private TenantApi tenantApi;

    /**
     * 设置相关参数 1.店主查审方记录 tenantId 2.药师查审方记录 有药师角色 + pharmacistPref、 3.总部查审方记录
     *
     * @param pageReqVO 查询参数
     */
    @Override
    public void setParam(InquiryPrescriptionPageReqVO pageReqVO) {
        // 如果前端选了门店就走门店，否则查询药师信息
        boolean isPharmacist = permissionApi.hasAnyRoles(Objects.requireNonNull(getLoginUser()).getId(), RoleCodeEnum.PHARMACIST.getCode());
        if (pageReqVO.getTenantId() == null && isPharmacist) {
            Optional.ofNullable(inquiryPharmacistApi.getPharmacistByUserId(Objects.requireNonNull(getLoginUser()).getId()))
                .ifPresent(pharmacistDto -> pageReqVO.setPharmacistPref(pharmacistDto.getPref()));
        }

        pageReqVO.setTenantIds(handleQueryTenantIds(tenantApi::getTenantIdsByHeadId, pageReqVO.getTenantId()));

        pageReqVO.setTenantId(null);
        // 药师需要根据审核时间查询
        pageReqVO.setAuditPrescriptionTime(pageReqVO.getOutPrescriptionTime());
        pageReqVO.setOutPrescriptionTime(null);
        pageReqVO.setStatuss(List.of(PrescriptionStatusEnum.WAIT_APPROVAL.getStatusCode(), PrescriptionStatusEnum.APPROVAL.getStatusCode(), PrescriptionStatusEnum.APPROVAL_REJECTED.getStatusCode()));
        pageReqVO.setEnable(Optional.ofNullable(pageReqVO.getEnable()).orElse(CommonStatusEnum.ENABLE.getStatus())); // 为空查可用的
    }

    /**
     * 获取当前策略对应的查询场景
     *
     * @return 查询场景
     */
    @Override
    public QuerySceneEnum getQueryScene() {
        return QuerySceneEnum.PHARMACIST;
    }
}
