package com.xyy.saas.inquiry.hospital.server.util;

import org.apache.commons.lang3.StringUtils;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;

/**
 * @ClassName：DateUtil
 * @Author: xucao
 * @Date: 2024/11/01 10:10
 * @Description: 日期工具类
 */
public class DateUtil {

    /**
     * 获取当前月的开始和上一个月的结束时间
     * <p>
     * 该方法用于计算当前月份的第一天和至  昨天结束时间的具体时间范围 它返回一个包含两个LocalDateTime对象的数组，第一个表示当前月的第一天的开始时间， 第二个表示昨天结束时间
     *
     * @return LocalDateTime[] 包含当前月开始和昨天结束时间
     */
    public static LocalDateTime[] getMonthStartAndPreviousMonthEnd() {
        // 获取当前月份的第一天
        LocalDate firstDayOfMonth = LocalDate.now().withDayOfMonth(1);
        // 将当前月份的第一天转换为当天的开始时间
        LocalDateTime startOfMonth = firstDayOfMonth.atStartOfDay();
        // 获取当前月份的第一天 至  今天的最后一刻
        LocalDateTime endOfMonth = LocalDate.now().atTime(LocalTime.MAX);
        // 返回当前月的开始和上一个月的结束时间
        return new LocalDateTime[]{startOfMonth, endOfMonth};
    }


    /**
     * 获取昨天的开始和结束时间
     * <p>
     * 本方法用于计算昨天的开始（00:00:00）和结束（23:59:59.999999999）时间，以LocalDateTime格式返回 这在统计或查询昨天数据时非常有用
     *
     * @return LocalDateTime[] 包含两个元素的数组，第一个元素是昨天的开始时间，第二个元素是昨天的结束时间
     */
    public static LocalDateTime[] getYesterdayStartAndEnd() {
        // 获取昨天的日期
        LocalDate yesterday = LocalDate.now().minusDays(1);
        // 将昨天的日期与时间00:00:00组合，得到昨天的开始时间
        LocalDateTime startOfDay = yesterday.atStartOfDay();
        // 将昨天的日期与最大时间23:59:59.999999999组合，得到昨天的结束时间
        LocalDateTime endOfDay = yesterday.atTime(LocalTime.MAX);
        // 返回昨天的开始和结束时间
        return new LocalDateTime[]{startOfDay, endOfDay};
    }


    public static LocalTime getEndLocalTime(String time) {
        if (StringUtils.equals(time, "00:00")) {
            return LocalTime.MAX;
        }
        return StringUtils.isBlank(time) ? LocalTime.now() : LocalTime.parse(time);

    }

}
