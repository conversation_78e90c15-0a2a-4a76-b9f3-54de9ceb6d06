package com.xyy.saas.inquiry.hospital.server.service.prescription;


import com.xyy.saas.inquiry.hospital.api.prescription.dto.InquiryPrescriptionUrlDTO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.prescription.vo.InquiryPrescriptionFlushReqVO;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.prescription.InquiryPrescriptionDO;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.prescription.InquiryPrescriptionDetailDO;
import com.xyy.saas.inquiry.patient.api.inquiry.dto.InquiryRecordDetailDto;
import java.util.List;

/**
 * 刷处方 Service 接口
 *
 * <AUTHOR>
 */
public interface InquiryPrescriptionFlushService {

    /**
     * 根据处方号刷单个处方
     *
     * @param pref
     * @return
     */
    InquiryPrescriptionUrlDTO flushSinglePrescription(String pref);

    /**
     * 仅刷处方金额
     *
     * @param pref
     * @return
     */
    InquiryPrescriptionUrlDTO flushPrescriptionPrice(String pref);

    /**
     * 仅刷处方时间
     *
     * @param prescriptionDOS
     * @return
     */
    List<InquiryPrescriptionDO> getPrescriptionsHandleDateType(List<InquiryPrescriptionDO> prescriptionDOS);


    /**
     * 接口刷处方
     *
     * @param flushReqVO
     */
    void flushInquiryPrescription(InquiryPrescriptionFlushReqVO flushReqVO);


    /**
     * 刷单个处方
     *
     * @param prescriptionDO
     * @param inquiryPrescriptionDetailDOS
     */
    InquiryPrescriptionUrlDTO executeFlushSinglePrescription(InquiryPrescriptionDO prescriptionDO
        , List<InquiryPrescriptionDetailDO> inquiryPrescriptionDetailDOS, InquiryRecordDetailDto inquiryRecordDto);
}